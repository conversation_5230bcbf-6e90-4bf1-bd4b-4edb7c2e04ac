{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"machine_shape": "hm", "gpuType": "T4", "provenance": []}, "accelerator": "GPU", "kaggle": {"accelerator": "gpu"}, "language_info": {"name": "python"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "markdown", "source": ["## Local Inference on GPU\n", "Model page: https://huggingface.co/DAMO-NLP-SG/VideoLLaMA3-7B\n", "\n", "⚠️ If the generated code snippets do not work, please open an issue on either the [model repo](https://huggingface.co/DAMO-NLP-SG/VideoLLaMA3-7B)\n", "\t\t\tand/or on [huggingface.js](https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/src/model-libraries-snippets.ts) 🙏"], "metadata": {"id": "VkrT__BFZDrI"}}, {"cell_type": "code", "source": ["!pip install ninja\n", "\n", "!git clone https://github.com/HazyResearch/flash-attention.git\n", "%cd flash-attention\n", "!python setup.py install"], "metadata": {"id": "0s2nyK_KZDrK", "outputId": "f6328104-b265-4265-8007-8d82fbf7e4b2", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting ninja\n", "  Downloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (5.0 kB)\n", "Downloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (422 kB)\n", "\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/422.8 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m422.8/422.8 kB\u001b[0m \u001b[31m25.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: ninja\n", "Successfully installed ninja-********\n", "Cloning into 'flash-attention'...\n", "remote: Enumerating objects: 10128, done.\u001b[K\n", "remote: Counting objects: 100% (2912/2912), done.\u001b[K\n", "remote: Compressing objects: 100% (258/258), done.\u001b[K\n", "remote: Total 10128 (delta 2763), reused 2662 (delta 2654), pack-reused 7216 (from 4)\u001b[K\n", "Receiving objects: 100% (10128/10128), 9.74 MiB | 18.17 MiB/s, done.\n", "Resolving deltas: 100% (7830/7830), done.\n", "/content/flash-attention\n", "Submodule 'csrc/composable_kernel' (https://github.com/ROCm/composable_kernel.git) registered for path 'csrc/composable_kernel'\n", "Cloning into '/content/flash-attention/csrc/composable_kernel'...\n", "Submodule path 'csrc/composable_kernel': checked out '663992e99b412991eab554b0deb89bb916d40161'\n", "Submodule 'csrc/cutlass' (https://github.com/NVIDIA/cutlass.git) registered for path 'csrc/cutlass'\n", "Cloning into '/content/flash-attention/csrc/cutlass'...\n", "Submodule path 'csrc/cutlass': checked out 'dc4817921edda44a549197ff3a9dcf5df0636e7b'\n", "\n", "\n", "torch.__version__  = 2.6.0+cu124\n", "\n", "\n", "/usr/local/lib/python3.11/dist-packages/setuptools/__init__.py:94: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.\n", "!!\n", "\n", "        ********************************************************************************\n", "        Requirements should be satisfied by a PEP 517 installer.\n", "        If you are using pip, you can try `pip install --use-pep517`.\n", "        ********************************************************************************\n", "\n", "!!\n", "  dist.fetch_build_eggs(dist.setup_requires)\n", "running install\n", "/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/cmd.py:66: SetuptoolsDeprecationWarning: setup.py install is deprecated.\n", "!!\n", "\n", "        ********************************************************************************\n", "        Please avoid running ``setup.py`` directly.\n", "        Instead, use pypa/build, pypa/installer or other\n", "        standards-based tools.\n", "\n", "        See https://blog.ganssle.io/articles/2021/10/setup-py-deprecated.html for details.\n", "        ********************************************************************************\n", "\n", "!!\n", "  self.initialize_options()\n", "/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/cmd.py:66: EasyInstallDeprecationWarning: easy_install command is deprecated.\n", "!!\n", "\n", "        ********************************************************************************\n", "        Please avoid running ``setup.py`` and ``easy_install``.\n", "        Instead, use pypa/build, pypa/installer or other\n", "        standards-based tools.\n", "\n", "        See https://github.com/pypa/setuptools/issues/917 for details.\n", "        ********************************************************************************\n", "\n", "!!\n", "  self.initialize_options()\n", "running bdist_egg\n", "running egg_info\n", "creating flash_attn.egg-info\n", "writing flash_attn.egg-info/PKG-INFO\n", "writing dependency_links to flash_attn.egg-info/dependency_links.txt\n", "writing requirements to flash_attn.egg-info/requires.txt\n", "writing top-level names to flash_attn.egg-info/top_level.txt\n", "writing manifest file 'flash_attn.egg-info/SOURCES.txt'\n", "reading manifest file 'flash_attn.egg-info/SOURCES.txt'\n", "reading manifest template 'MANIFEST.in'\n", "warning: no files found matching '*.cu' under directory 'flash_attn'\n", "warning: no files found matching '*.h' under directory 'flash_attn'\n", "warning: no files found matching '*.cuh' under directory 'flash_attn'\n", "warning: no files found matching '*.cpp' under directory 'flash_attn'\n", "warning: no files found matching '*.hpp' under directory 'flash_attn'\n", "adding license file 'LICENSE'\n", "adding license file 'AUTHORS'\n", "writing manifest file 'flash_attn.egg-info/SOURCES.txt'\n", "installing library code to build/bdist.linux-x86_64/egg\n", "running install_lib\n", "running build_py\n", "creating build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/benchmark_attn.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/setup.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/test_attn_kvcache.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/flash_attn_interface.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/test_kvcache.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/__init__.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/test_util.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/benchmark_mla_decode.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/padding.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/generate_kernels.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/test_flash_attn.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/benchmark_flash_attention_fp8.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "copying hopper/benchmark_split_kv.py -> build/lib.linux-x86_64-cpython-311/hopper\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/flash_blocksparse_attention.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/flash_attn_interface.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/fused_softmax.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/flash_blocksparse_attn_interface.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/flash_attn_triton.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/bert_padding.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "copying flash_attn/flash_attn_triton_og.py -> build/lib.linux-x86_64-cpython-311/flash_attn\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bwd_prefill.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bwd_prefill_onekernel.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bwd_prefill_split.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/interface_fa.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/fwd_prefill.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/utils.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/test.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/fwd_ref.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/fwd_decode.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/train.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bwd_prefill_fused.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bwd_ref.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/fp8.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "copying flash_attn/flash_attn_triton_amd/bench.py -> build/lib.linux-x86_64-cpython-311/flash_attn/flash_attn_triton_amd\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/layers\n", "copying flash_attn/layers/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/layers\n", "copying flash_attn/layers/rotary.py -> build/lib.linux-x86_64-cpython-311/flash_attn/layers\n", "copying flash_attn/layers/patch_embed.py -> build/lib.linux-x86_64-cpython-311/flash_attn/layers\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/testing.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/distributed.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/library.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/generation.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/torch.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/pretrained.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "copying flash_attn/utils/benchmark.py -> build/lib.linux-x86_64-cpython-311/flash_attn/utils\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/losses\n", "copying flash_attn/losses/cross_entropy.py -> build/lib.linux-x86_64-cpython-311/flash_attn/losses\n", "copying flash_attn/losses/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/losses\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "copying flash_attn/ops/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "copying flash_attn/ops/activations.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "copying flash_attn/ops/fused_dense.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "copying flash_attn/ops/layer_norm.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "copying flash_attn/ops/rms_norm.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/gpt.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/llama.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/falcon.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/baichuan.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/bigcode.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/bert.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/vit.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/gpt_neox.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/opt.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/btlm.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "copying flash_attn/models/gptj.py -> build/lib.linux-x86_64-cpython-311/flash_attn/models\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "copying flash_attn/modules/block.py -> build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "copying flash_attn/modules/mlp.py -> build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "copying flash_attn/modules/mha.py -> build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "copying flash_attn/modules/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "copying flash_attn/modules/embedding.py -> build/lib.linux-x86_64-cpython-311/flash_attn/modules\n", "creating build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/k_activations.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/cross_entropy.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/mlp.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/linear.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/__init__.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/rotary.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "copying flash_attn/ops/triton/layer_norm.py -> build/lib.linux-x86_64-cpython-311/flash_attn/ops/triton\n", "running build_ext\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py:448: UserWarning: The detected CUDA version (12.5) has a minor version mismatch with the version that was used to compile PyTorch (12.4). Most likely this shouldn't be a problem.\n", "  warnings.warn(CUDA_MISMATCH_WARN.format(cuda_str_version, torch.version.cuda))\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py:458: UserWarning: There are no x86_64-linux-gnu-g++ version bounds defined for CUDA version 12.5\n", "  warnings.warn(f'There are no {compiler_name} version bounds defined for CUDA version {cuda_str_version}')\n", "building 'flash_attn_2_cuda' extension\n", "creating /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn\n", "creating /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src\n", "Emitting ninja build file /content/flash-attention/build/temp.linux-x86_64-cpython-311/build.ninja...\n", "Compiling objects...\n", "Using envvar MAX_JOBS (1) as the number of workers...\n", "[1/73] c++ -<PERSON><PERSON> -MF /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/flash_api.o.d -Wsign-compare -DNDEBUG -g -fwrapv -O2 -Wall -g -fstack-protector-strong -Wformat -Werror=format-security -g -fwrapv -O2 -fPIC -I/content/flash-attention/csrc/flash_attn -I/content/flash-attention/csrc/flash_attn/src -I/content/flash-attention/csrc/cutlass/include -I/usr/local/lib/python3.11/dist-packages/torch/include -I/usr/local/lib/python3.11/dist-packages/torch/include/torch/csrc/api/include -I/usr/local/lib/python3.11/dist-packages/torch/include/TH -I/usr/local/lib/python3.11/dist-packages/torch/include/THC -I/usr/local/cuda/include -I/usr/include/python3.11 -c -c /content/flash-attention/csrc/flash_attn/flash_api.cpp -o /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/flash_api.o -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE=\"_gcc\"' '-DPYBIND11_STDLIB=\"_libstdcpp\"' '-DPYBIND11_BUILD_ABI=\"_cxxabi1011\"' -DTORCH_EXTENSION_NAME=flash_attn_2_cuda -D_GLIBCXX_USE_CXX11_ABI=0\n", "[2/73] /usr/local/cuda/bin/nvcc --generate-dependencies-with-compile --dependency-output /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.o.d -I/content/flash-attention/csrc/flash_attn -I/content/flash-attention/csrc/flash_attn/src -I/content/flash-attention/csrc/cutlass/include -I/usr/local/lib/python3.11/dist-packages/torch/include -I/usr/local/lib/python3.11/dist-packages/torch/include/torch/csrc/api/include -I/usr/local/lib/python3.11/dist-packages/torch/include/TH -I/usr/local/lib/python3.11/dist-packages/torch/include/THC -I/usr/local/cuda/include -I/usr/include/python3.11 -c -c /content/flash-attention/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.cu -o /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.o -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''\"'\"'-fPIC'\"'\"'' -O3 -std=c++17 -U__CUDA_NO_HALF_OPERATORS__ -U__CUDA_NO_HALF_CONVERSIONS__ -U__CUDA_NO_HALF2_OPERATORS__ -U__CUDA_NO_BFLOAT16_CONVERSIONS__ --expt-relaxed-constexpr --expt-extended-lambda --use_fast_math -gencode arch=compute_80,code=sm_80 -gencode arch=compute_90,code=sm_90 --threads 4 -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE=\"_gcc\"' '-DPYBIND11_STDLIB=\"_libstdcpp\"' '-DPYBIND11_BUILD_ABI=\"_cxxabi1011\"' -DTORCH_EXTENSION_NAME=flash_attn_2_cuda -D_GLIBCXX_USE_CXX11_ABI=0\n", "\u001b[31mFAILED: \u001b[0m/content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.o \n", "/usr/local/cuda/bin/nvcc --generate-dependencies-with-compile --dependency-output /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.o.d -I/content/flash-attention/csrc/flash_attn -I/content/flash-attention/csrc/flash_attn/src -I/content/flash-attention/csrc/cutlass/include -I/usr/local/lib/python3.11/dist-packages/torch/include -I/usr/local/lib/python3.11/dist-packages/torch/include/torch/csrc/api/include -I/usr/local/lib/python3.11/dist-packages/torch/include/TH -I/usr/local/lib/python3.11/dist-packages/torch/include/THC -I/usr/local/cuda/include -I/usr/include/python3.11 -c -c /content/flash-attention/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.cu -o /content/flash-attention/build/temp.linux-x86_64-cpython-311/csrc/flash_attn/src/flash_bwd_hdim128_bf16_causal_sm80.o -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''\"'\"'-fPIC'\"'\"'' -O3 -std=c++17 -U__CUDA_NO_HALF_OPERATORS__ -U__CUDA_NO_HALF_CONVERSIONS__ -U__CUDA_NO_HALF2_OPERATORS__ -U__CUDA_NO_BFLOAT16_CONVERSIONS__ --expt-relaxed-constexpr --expt-extended-lambda --use_fast_math -gencode arch=compute_80,code=sm_80 -gencode arch=compute_90,code=sm_90 --threads 4 -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE=\"_gcc\"' '-DPYBIND11_STDLIB=\"_libstdcpp\"' '-DPYBIND11_BUILD_ABI=\"_cxxabi1011\"' -DTORCH_EXTENSION_NAME=flash_attn_2_cuda -D_GLIBCXX_USE_CXX11_ABI=0\n", "Killed\n", "ninja: build stopped: subcommand failed.\n", "Traceback (most recent call last):\n", "  File \"/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py\", line 2209, in _run_ninja_build\n", "    subprocess.run(\n", "  File \"/usr/lib/python3.11/subprocess.py\", line 571, in run\n", "    raise CalledProcessError(retcode, process.args,\n", "subprocess.CalledProcessError: Command '['ninja', '-v', '-j', '1']' returned non-zero exit status 1.\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/content/flash-attention/setup.py\", line 519, in <module>\n", "    setup(\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/__init__.py\", line 117, in setup\n", "    return distutils.core.setup(**attrs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/core.py\", line 183, in setup\n", "    return run_commands(dist)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/core.py\", line 199, in run_commands\n", "    dist.run_commands()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/dist.py\", line 954, in run_commands\n", "    self.run_command(cmd)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/dist.py\", line 991, in run_command\n", "    super().run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/dist.py\", line 973, in run_command\n", "    cmd_obj.run()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/install.py\", line 97, in run\n", "    self.do_egg_install()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/install.py\", line 149, in do_egg_install\n", "    self.run_command('bdist_egg')\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/cmd.py\", line 316, in run_command\n", "    self.distribution.run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/dist.py\", line 991, in run_command\n", "    super().run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/dist.py\", line 973, in run_command\n", "    cmd_obj.run()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/bdist_egg.py\", line 168, in run\n", "    cmd = self.call_command('install_lib', warn_dir=False)\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/bdist_egg.py\", line 154, in call_command\n", "    self.run_command(cmdname)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/cmd.py\", line 316, in run_command\n", "    self.distribution.run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/dist.py\", line 991, in run_command\n", "    super().run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/dist.py\", line 973, in run_command\n", "    cmd_obj.run()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/install_lib.py\", line 19, in run\n", "    self.build()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/command/install_lib.py\", line 110, in build\n", "    self.run_command('build_ext')\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/cmd.py\", line 316, in run_command\n", "    self.distribution.run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/dist.py\", line 991, in run_command\n", "    super().run_command(command)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/dist.py\", line 973, in run_command\n", "    cmd_obj.run()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/build_ext.py\", line 98, in run\n", "    _build_ext.run(self)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/command/build_ext.py\", line 359, in run\n", "    self.build_extensions()\n", "  File \"/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py\", line 900, in build_extensions\n", "    build_ext.build_extensions(self)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/command/build_ext.py\", line 476, in build_extensions\n", "    self._build_extensions_serial()\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/command/build_ext.py\", line 502, in _build_extensions_serial\n", "    self.build_extension(ext)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/command/build_ext.py\", line 263, in build_extension\n", "    _build_ext.build_extension(self, ext)\n", "  File \"/usr/local/lib/python3.11/dist-packages/Cython/Distutils/build_ext.py\", line 135, in build_extension\n", "    super(build_ext, self).build_extension(ext)\n", "  File \"/usr/local/lib/python3.11/dist-packages/setuptools/_distutils/command/build_ext.py\", line 557, in build_extension\n", "    objects = self.compiler.compile(\n", "              ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py\", line 713, in unix_wrap_ninja_compile\n", "    _write_ninja_file_and_compile_objects(\n", "  File \"/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py\", line 1869, in _write_ninja_file_and_compile_objects\n", "    _run_ninja_build(\n", "  File \"/usr/local/lib/python3.11/dist-packages/torch/utils/cpp_extension.py\", line 2225, in _run_ninja_build\n", "    raise RuntimeError(message) from e\n", "RuntimeError: Error compiling objects for extension\n"]}]}, {"cell_type": "code", "source": ["!git clone https://github.com/mbzuai-oryx/VideoGPT-plus\n", "%cd VideoGPT-plus\n", "\n", "!pip install torch==2.1.2 torchvision==0.16.2 --index-url https://download.pytorch.org/whl/cu118\n", "!pip install transformers==4.41.0\n", "\n", "!pip install -r requirements.txt"], "metadata": {"id": "hv44Mj1dZDrK", "outputId": "c404a4b9-1f46-4f67-aea6-f43ee51da8cd", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cloning into 'VideoGPT-plus'...\n", "remote: Enumerating objects: 128, done.\u001b[K\n", "remote: Counting objects: 100% (128/128), done.\u001b[K\n", "remote: Compressing objects: 100% (106/106), done.\u001b[K\n", "remote: Total 128 (delta 30), reused 106 (delta 17), pack-reused 0 (from 0)\u001b[K\n", "Receiving objects: 100% (128/128), 16.85 MiB | 9.66 MiB/s, done.\n", "Resolving deltas: 100% (30/30), done.\n", "/content/flash-attention/VideoGPT-plus\n", "Looking in indexes: https://download.pytorch.org/whl/cu118\n", "Collecting torch==2.1.2\n", "  Downloading https://download.pytorch.org/whl/cu118/torch-2.1.2%2Bcu118-cp311-cp311-linux_x86_64.whl (2325.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 GB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting torchvision==0.16.2\n", "  Downloading https://download.pytorch.org/whl/cu118/torchvision-0.16.2%2Bcu118-cp311-cp311-linux_x86_64.whl (6.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.1/6.1 MB\u001b[0m \u001b[31m102.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (3.18.0)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (4.14.0)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (1.13.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch==2.1.2) (2025.3.2)\n", "Collecting triton==2.1.0 (from torch==2.1.2)\n", "  Downloading https://download.pytorch.org/whl/triton-2.1.0-0-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (89.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.2/89.2 MB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from torchvision==0.16.2) (2.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from torchvision==0.16.2) (2.32.3)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision==0.16.2) (11.2.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch==2.1.2) (3.0.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->torchvision==0.16.2) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->torchvision==0.16.2) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->torchvision==0.16.2) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->torchvision==0.16.2) (2025.6.15)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy->torch==2.1.2) (1.3.0)\n", "Installing collected packages: triton, torch, torchvision\n", "  Attempting uninstall: triton\n", "    Found existing installation: triton 3.2.0\n", "    Uninstalling triton-3.2.0:\n", "      Successfully uninstalled triton-3.2.0\n", "  Attempting uninstall: torch\n", "    Found existing installation: torch 2.6.0+cu124\n", "    Uninstalling torch-2.6.0+cu124:\n", "      Successfully uninstalled torch-2.6.0+cu124\n", "  Attempting uninstall: torchvision\n", "    Found existing installation: torchvision 0.21.0+cu124\n", "    Uninstalling torchvision-0.21.0+cu124:\n", "      Successfully uninstalled torchvision-0.21.0+cu124\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "torchaudio 2.6.0+cu124 requires torch==2.6.0, but you have torch 2.1.2+cu118 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed torch-2.1.2+cu118 torchvision-0.16.2+cu118 triton-2.1.0\n", "Collecting transformers==4.41.0\n", "  Downloading transformers-4.41.0-py3-none-any.whl.metadata (43 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.8/43.8 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (0.33.1)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (2.32.3)\n", "Collecting tokenizers<0.20,>=0.19 (from transformers==4.41.0)\n", "  Downloading tokenizers-0.19.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers==4.41.0) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.23.0->transformers==4.41.0) (2025.3.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.23.0->transformers==4.41.0) (4.14.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.23.0->transformers==4.41.0) (1.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.41.0) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.41.0) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.41.0) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.41.0) (2025.6.15)\n", "Downloading transformers-4.41.0-py3-none-any.whl (9.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m9.1/9.1 MB\u001b[0m \u001b[31m85.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tokenizers-0.19.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.6/3.6 MB\u001b[0m \u001b[31m95.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tokenizers, transformers\n", "  Attempting uninstall: tokenizers\n", "    Found existing installation: tokenizers 0.21.2\n", "    Uninstalling tokenizers-0.21.2:\n", "      Successfully uninstalled tokenizers-0.21.2\n", "  Attempting uninstall: transformers\n", "    Found existing installation: transformers 4.53.0\n", "    Uninstalling transformers-4.53.0:\n", "      Successfully uninstalled transformers-4.53.0\n", "Successfully installed tokenizers-0.19.1 transformers-4.41.0\n", "Requirement already satisfied: tokenizers in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 1)) (0.19.1)\n", "Requirement already satisfied: sentencepiece in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 2)) (0.2.0)\n", "Collecting shortuuid (from -r requirements.txt (line 3))\n", "  Downloading shortuuid-1.0.13-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: accelerate in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 4)) (1.8.1)\n", "Requirement already satisfied: peft in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 5)) (0.15.2)\n", "Collecting bitsandbytes (from -r requirements.txt (line 6))\n", "  Downloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl.metadata (10 kB)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 7)) (2.11.7)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 9)) (2.0.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 10)) (1.6.1)\n", "Requirement already satisfied: gradio in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 11)) (5.31.0)\n", "Requirement already satisfied: gradio_client in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 12)) (1.10.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 13)) (2.32.3)\n", "Requirement already satisfied: httpx in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 14)) (0.28.1)\n", "Requirement already satisfied: uvicorn in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 15)) (0.35.0)\n", "Requirement already satisfied: fastapi in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 16)) (0.115.14)\n", "Requirement already satisfied: einops in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 17)) (0.8.1)\n", "Collecting einops-exts (from -r requirements.txt (line 18))\n", "  Downloading einops_exts-0.0.4-py3-none-any.whl.metadata (621 bytes)\n", "Requirement already satisfied: timm in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 19)) (1.0.16)\n", "Collecting markdown2[all] (from -r requirements.txt (line 8))\n", "  Downloading markdown2-2.5.3-py3-none-any.whl.metadata (2.1 kB)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.11/dist-packages (from tokenizers->-r requirements.txt (line 1)) (0.33.1)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from accelerate->-r requirements.txt (line 4)) (24.2)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate->-r requirements.txt (line 4)) (5.9.5)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.11/dist-packages (from accelerate->-r requirements.txt (line 4)) (6.0.2)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from accelerate->-r requirements.txt (line 4)) (2.1.2+cu118)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from accelerate->-r requirements.txt (line 4)) (0.5.3)\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (from peft->-r requirements.txt (line 5)) (4.41.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from peft->-r requirements.txt (line 5)) (4.67.1)\n", "Collecting torch>=2.0.0 (from accelerate->-r requirements.txt (line 4))\n", "  Downloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (29 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic->-r requirements.txt (line 7)) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic->-r requirements.txt (line 7)) (2.33.2)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in /usr/local/lib/python3.11/dist-packages (from pydantic->-r requirements.txt (line 7)) (4.14.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic->-r requirements.txt (line 7)) (0.4.1)\n", "Requirement already satisfied: pygments>=2.7.3 in /usr/local/lib/python3.11/dist-packages (from markdown2[all]->-r requirements.txt (line 8)) (2.19.2)\n", "Collecting wavedrom (from markdown2[all]->-r requirements.txt (line 8))\n", "  Downloading wavedrom-2.0.3.post3.tar.gz (137 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m137.7/137.7 kB\u001b[0m \u001b[31m12.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting latex2mathml (from markdown2[all]->-r requirements.txt (line 8))\n", "  Downloading latex2mathml-3.78.0-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->-r requirements.txt (line 10)) (1.15.3)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->-r requirements.txt (line 10)) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->-r requirements.txt (line 10)) (3.6.0)\n", "Requirement already satisfied: aiofiles<25.0,>=22.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (24.1.0)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (4.9.0)\n", "Requirement already satisfied: ffmpy in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.6.0)\n", "Requirement already satisfied: groovy~=0.1 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.1.2)\n", "Requirement already satisfied: jinja2<4.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (3.1.6)\n", "Requirement already satisfied: markupsafe<4.0,>=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (3.0.2)\n", "Requirement already satisfied: orjson~=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (3.10.18)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (2.2.2)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (11.2.1)\n", "Requirement already satisfied: pydub in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.25.1)\n", "Requirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.0.20)\n", "Requirement already satisfied: ruff>=0.9.3 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.12.1)\n", "Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.1.6)\n", "Requirement already satisfied: semantic-version~=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (2.10.0)\n", "Requirement already satisfied: starlette<1.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.46.2)\n", "Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.13.3)\n", "Requirement already satisfied: typer<1.0,>=0.12 in /usr/local/lib/python3.11/dist-packages (from gradio->-r requirements.txt (line 11)) (0.16.0)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from gradio_client->-r requirements.txt (line 12)) (2025.3.2)\n", "Requirement already satisfied: websockets<16.0,>=10.0 in /usr/local/lib/python3.11/dist-packages (from gradio_client->-r requirements.txt (line 12)) (15.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->-r requirements.txt (line 13)) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->-r requirements.txt (line 13)) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->-r requirements.txt (line 13)) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->-r requirements.txt (line 13)) (2025.6.15)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx->-r requirements.txt (line 14)) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx->-r requirements.txt (line 14)) (0.16.0)\n", "Requirement already satisfied: click>=7.0 in /usr/local/lib/python3.11/dist-packages (from uvicorn->-r requirements.txt (line 15)) (8.2.1)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (from timm->-r requirements.txt (line 19)) (0.16.2+cu118)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio->-r requirements.txt (line 11)) (1.3.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->-r requirements.txt (line 1)) (3.18.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->-r requirements.txt (line 1)) (1.1.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio->-r requirements.txt (line 11)) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio->-r requirements.txt (line 11)) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio->-r requirements.txt (line 11)) (2025.2)\n", "Collecting sympy>=1.13.3 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate->-r requirements.txt (line 4)) (3.5)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.6.77 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.6.77 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.6.80 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux_2_28_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********* (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_curand_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cusparse_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparselt-cu12==0.6.3 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Collecting nvidia-nccl-cu12==2.26.2 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.0 kB)\n", "Collecting nvidia-nvtx-cu12==12.6.77 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-nvjitlink-cu12==12.6.85 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufile-cu12==******** (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading nvidia_cufile_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting triton==3.3.1 (from torch>=2.0.0->accelerate->-r requirements.txt (line 4))\n", "  Downloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: setuptools>=40.8.0 in /usr/local/lib/python3.11/dist-packages (from triton==3.3.1->torch>=2.0.0->accelerate->-r requirements.txt (line 4)) (75.2.0)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio->-r requirements.txt (line 11)) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio->-r requirements.txt (line 11)) (13.9.4)\n", "INFO: pip is looking at multiple versions of torchvision to determine which version is compatible with other requirements. This could take a while.\n", "Collecting torchvision (from timm->-r requirements.txt (line 19))\n", "  Downloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.1 kB)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers->peft->-r requirements.txt (line 5)) (2024.11.6)\n", "Collecting svgwrite (from wavedrom->markdown2[all]->-r requirements.txt (line 8))\n", "  Downloading svgwrite-1.4.3-py3-none-any.whl.metadata (8.8 kB)\n", "Requirement already satisfied: six in /usr/local/lib/python3.11/dist-packages (from wavedrom->markdown2[all]->-r requirements.txt (line 8)) (1.17.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio->-r requirements.txt (line 11)) (3.0.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate->-r requirements.txt (line 4)) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio->-r requirements.txt (line 11)) (0.1.2)\n", "Downloading shortuuid-1.0.13-py3-none-any.whl (10 kB)\n", "Downloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl (72.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.9/72.9 MB\u001b[0m \u001b[31m12.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading einops_exts-0.0.4-py3-none-any.whl (3.9 kB)\n", "Downloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl (821.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m821.2/821.2 MB\u001b[0m \u001b[31m1.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.1/393.1 MB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (8.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m120.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl (23.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.7/23.7 MB\u001b[0m \u001b[31m92.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (897 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.7/897.7 kB\u001b[0m \u001b[31m56.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux_2_28_x86_64.whl (571.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m571.0/571.0 MB\u001b[0m \u001b[31m1.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (200.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m200.2/200.2 MB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufile_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m41.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-*********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m15.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m158.2/158.2 MB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (216.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m216.6/216.6 MB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl (156.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m156.8/156.8 MB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (201.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.3/201.3 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl (19.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.7/19.7 MB\u001b[0m \u001b[31m42.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (89 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.3/89.3 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (155.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m155.7/155.7 MB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading latex2mathml-3.78.0-py3-none-any.whl (73 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m73.7/73.7 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading markdown2-2.5.3-py3-none-any.whl (48 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.5/48.5 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl (7.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.5/7.5 MB\u001b[0m \u001b[31m97.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m88.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading svgwrite-1.4.3-py3-none-any.whl (67 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.1/67.1 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: wavedrom\n", "  Building wheel for wavedrom (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for wavedrom: filename=wavedrom-2.0.3.post3-py2.py3-none-any.whl size=30084 sha256=6a230fa57586ee6cac7c680451efbb165e71055b3d2772604a83aa0902de76bb\n", "  Stored in directory: /root/.cache/pip/wheels/23/cf/3b/4dcf6b22fa41c5ece715fa5f4e05afd683e7b0ce0f2fcc7bb6\n", "Successfully built wavedrom\n", "Installing collected packages: nvidia-cusparselt-cu12, triton, sympy, svgwrite, shortuuid, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufile-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, markdown2, latex2mathml, einops-exts, wavedrom, nvidia-cusparse-cu12, nvidia-cufft-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, torch, torchvision, bitsandbytes\n", "  Attempting uninstall: nvidia-cusparselt-cu12\n", "    Found existing installation: nvidia-cusparselt-cu12 0.6.2\n", "    Uninstalling nvidia-cusparselt-cu12-0.6.2:\n", "      Successfully uninstalled nvidia-cusparselt-cu12-0.6.2\n", "  Attempting uninstall: triton\n", "    Found existing installation: triton 2.1.0\n", "    Uninstalling triton-2.1.0:\n", "      Successfully uninstalled triton-2.1.0\n", "  Attempting uninstall: sympy\n", "    Found existing installation: sympy 1.13.1\n", "    Uninstalling sympy-1.13.1:\n", "      Successfully uninstalled sympy-1.13.1\n", "  Attempting uninstall: nvidia-nvtx-cu12\n", "    Found existing installation: nvidia-nvtx-cu12 12.4.127\n", "    Uninstalling nvidia-nvtx-cu12-12.4.127:\n", "      Successfully uninstalled nvidia-nvtx-cu12-12.4.127\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-nccl-cu12\n", "    Found existing installation: nvidia-nccl-cu12 2.21.5\n", "    Uninstalling nvidia-nccl-cu12-2.21.5:\n", "      Successfully uninstalled nvidia-nccl-cu12-2.21.5\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "  Attempting uninstall: torch\n", "    Found existing installation: torch 2.1.2+cu118\n", "    Uninstalling torch-2.1.2+cu118:\n", "      Successfully uninstalled torch-2.1.2+cu118\n", "  Attempting uninstall: torchvision\n", "    Found existing installation: torchvision 0.16.2+cu118\n", "    Uninstalling torchvision-0.16.2+cu118:\n", "      Successfully uninstalled torchvision-0.16.2+cu118\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "fastai 2.7.19 requires torch<2.7,>=1.10, but you have torch 2.7.1 which is incompatible.\n", "torchaudio 2.6.0+cu124 requires torch==2.6.0, but you have torch 2.7.1 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed bitsandbytes-0.46.1 einops-exts-0.0.4 latex2mathml-3.78.0 markdown2-2.5.3 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.6.80 nvidia-cuda-nvrtc-cu12-12.6.77 nvidia-cuda-runtime-cu12-12.6.77 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-cufile-cu12-******** nvidia-curand-cu12-********* nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-******** nvidia-cusparselt-cu12-0.6.3 nvidia-nccl-cu12-2.26.2 nvidia-nvjitlink-cu12-12.6.85 nvidia-nvtx-cu12-12.6.77 shortuuid-1.0.13 svgwrite-1.4.3 sympy-1.14.0 torch-2.7.1 torchvision-0.22.1 triton-3.3.1 wavedrom-2.0.3.post3\n"]}]}, {"cell_type": "code", "source": ["%cd /content/VideoGPT-plus\n", "!python setup.py develop\n"], "metadata": {"id": "1EDrXcHFaNb-", "outputId": "3db7cc85-5330-40a8-9b4a-3828d1b7dee3", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Errno 2] No such file or directory: '/content/VideoGPT-plus'\n", "/content/flash-attention/VideoGPT-plus\n", "python3: can't open file '/content/flash-attention/VideoGPT-plus/setup.py': [Errno 2] No such file or directory\n"]}]}, {"cell_type": "code", "source": ["!mkdir -p pretrained\n", "!wget https://huggingface.co/oryx-a1/VideoGPT-plus/resolve/main/videogpt_plus.pt -O pretrained/videogpt_plus.pt\n"], "metadata": {"id": "7nHOkBZ-abLs", "outputId": "43abebda-fa5c-49bd-9020-4fc7fe27066a", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--2025-07-07 10:32:17--  https://huggingface.co/oryx-a1/VideoGPT-plus/resolve/main/videogpt_plus.pt\n", "Resolving huggingface.co (huggingface.co)... ************, *************, ************, ...\n", "Connecting to huggingface.co (huggingface.co)|************|:443... connected.\n", "HTTP request sent, awaiting response... 401 Unauthorized\n", "\n", "Username/Password Authentication Failed.\n"]}]}, {"cell_type": "code", "source": ["%cd /content/flash-attention/VideoGPT-plus\n", "import sys\n", "sys.path.append(\"/content/flash-attention/VideoGPT-plus\")\n", "\n", "from videogpt_plus.models.videogpt_plus import VideoGPTPlus\n"], "metadata": {"id": "PF5Vm3vvcwW8", "outputId": "7068ee0c-8d93-4885-96a0-21dd30d18957", "colab": {"base_uri": "https://localhost:8080/", "height": 366}}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content/flash-attention/VideoGPT-plus\n"]}, {"output_type": "error", "ename": "ModuleNotFoundError", "evalue": "No module named 'videogpt_plus.models'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-18-2538604014.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0msys\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"/content/flash-attention/VideoGPT-plus\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mvideogpt_plus\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodels\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvideogpt_plus\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mVideoGPTPlus\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'videogpt_plus.models'", "", "\u001b[0;31m---------------------------------------------------------------------------\u001b[0;32m\nNOTE: If your import is failing due to a missing package, you can\nmanually install dependencies using either !pip or !apt.\n\nTo view examples of installing some common dependencies, click the\n\"Open Examples\" button below.\n\u001b[0;31m---------------------------------------------------------------------------\u001b[0m\n"], "errorDetails": {"actions": [{"action": "open_url", "actionText": "Open Examples", "url": "/notebooks/snippets/importing_libraries.ipynb"}]}}]}, {"cell_type": "code", "source": ["import torch\n", "\n", "from videogpt_plus.models.videogpt_plus import VideoGPTPlus\n", "from torchvision.io import read_video\n", "import matplotlib.pyplot as plt\n", "\n", "# Load pretrained model\n", "model_path = \"pretrained/videogpt_plus.pt\"\n", "model = VideoGPTPlus.load_from_checkpoint(model_path)\n", "model.eval().cuda()\n", "\n", "# Load test video (or upload one)\n", "video_path = \"/content/test.mp4\"  # <- update with your video\n", "video, _, _ = read_video(video_path)  # (T, H, W, C)\n", "video = video[:16]  # Trim to 16 frames if needed\n", "video = video.permute(0, 3, 1, 2).unsqueeze(0).float().cuda() / 255.0  # (1, T, C, H, W)\n", "\n", "# Encode and Decode\n", "with torch.no_grad():\n", "    z = model.encode(video)\n", "    out = model.decode(z)\n", "\n", "# Plot a few output frames\n", "out = (out[0] * 255).byte().permute(0, 2, 3, 1).cpu()  # (T, H, W, C)\n", "\n", "for i in range(min(5, out.shape[0])):\n", "    plt.imshow(out[i].numpy())\n", "    plt.title(f\"Frame {i}\")\n", "    plt.axis('off')\n", "    plt.show()\n"], "metadata": {"id": "cWMnpiS_ahp6", "outputId": "d4002c9d-2a89-4968-fe62-973696cc856d", "colab": {"base_uri": "https://localhost:8080/", "height": 383}}, "execution_count": 17, "outputs": [{"output_type": "error", "ename": "ModuleNotFoundError", "evalue": "No module named 'videogpt_plus.models'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-17-**********.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mtorch\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mvideogpt_plus\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodels\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvideogpt_plus\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mVideoGPTPlus\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mtorchvision\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mio\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mread_video\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mmatplotlib\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpyplot\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mplt\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'videogpt_plus.models'", "", "\u001b[0;31m---------------------------------------------------------------------------\u001b[0;32m\nNOTE: If your import is failing due to a missing package, you can\nmanually install dependencies using either !pip or !apt.\n\nTo view examples of installing some common dependencies, click the\n\"Open Examples\" button below.\n\u001b[0;31m---------------------------------------------------------------------------\u001b[0m\n"], "errorDetails": {"actions": [{"action": "open_url", "actionText": "Open Examples", "url": "/notebooks/snippets/importing_libraries.ipynb"}]}}]}]}