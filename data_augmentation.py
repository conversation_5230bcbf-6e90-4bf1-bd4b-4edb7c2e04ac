import torch
import torch.nn as nn
import cv2
import numpy as np
import os
from pathlib import Path
import random
from tqdm import tqdm

class VideoAugmentation:
    def __init__(self, target_size=(224, 224)):
        self.target_size = target_size
        
        # Define augmentation pipeline
        self.augmentation_pipeline = <PERSON><PERSON>([
            # Spatial augmentations
            A.<PERSON>(p=0.5),
            <PERSON><PERSON>rightness<PERSON>ontrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
            <PERSON><PERSON>(gamma_limit=(80, 120), p=0.3),
            <PERSON><PERSON>(var_limit=(10.0, 50.0), p=0.3),
            <PERSON><PERSON>(blur_limit=3, p=0.2),
            <PERSON><PERSON>ota<PERSON>(p=0.2),
            <PERSON><PERSON>ft<PERSON>(shift_limit=0.1, scale_limit=0.1, rotate_limit=15, p=0.3),
            A.<PERSON>asticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.2),
            <PERSON><PERSON>(p=0.2),
            <PERSON><PERSON>(distort_limit=0.1, shift_limit=0.1, p=0.2),
        ])
        
        # Temporal augmentations
        self.temporal_augmentations = [
            'speed_change',
            'frame_dropout',
            'temporal_shift',
            'reverse_playback'
        ]
    
    def apply_spatial_augmentation(self, frame):
        """Apply spatial augmentations to a single frame"""
        try:
            augmented = self.augmentation_pipeline(image=frame)
            return augmented['image']
        except:
            return frame
    
    def apply_temporal_augmentation(self, frames, aug_type):
        """Apply temporal augmentations to frame sequence"""
        if aug_type == 'speed_change':
            # Randomly change playback speed by sampling different frames
            if random.random() < 0.5:  # Speed up
                indices = np.linspace(0, len(frames)-1, len(frames)//2, dtype=int)
                frames = [frames[i] for i in indices]
                # Duplicate frames to maintain length
                while len(frames) < 16:
                    frames.extend(frames[:16-len(frames)])
            else:  # Slow down
                # Duplicate some frames
                new_frames = []
                for i, frame in enumerate(frames):
                    new_frames.append(frame)
                    if random.random() < 0.3 and len(new_frames) < 16:
                        new_frames.append(frame)  # Duplicate frame
                frames = new_frames[:16]
        
        elif aug_type == 'frame_dropout':
            # Randomly drop some frames and duplicate others
            dropout_prob = 0.2
            new_frames = []
            for frame in frames:
                if random.random() > dropout_prob:
                    new_frames.append(frame)
            # Fill missing frames by duplicating existing ones
            while len(new_frames) < len(frames):
                new_frames.append(random.choice(new_frames))
            frames = new_frames[:16]
        
        elif aug_type == 'temporal_shift':
            # Shift the temporal sequence
            shift = random.randint(-2, 2)
            if shift > 0:
                frames = frames[shift:] + frames[:shift]
            elif shift < 0:
                frames = frames[shift:] + frames[:shift]
        
        elif aug_type == 'reverse_playback':
            # Reverse the video sequence
            if random.random() < 0.3:
                frames = frames[::-1]
        
        return frames
    
    def augment_video(self, video_path, num_augmentations=3):
        """Generate augmented versions of a video"""
        augmented_videos = []
        
        # Load original video
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames == 0:
            cap.release()
            return []
        
        # Extract frames
        frame_indices = np.linspace(0, total_frames - 1, 16, dtype=int)
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                frame = cv2.resize(frame, self.target_size)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
        
        cap.release()
        
        if len(frames) < 16:
            return []
        
        # Generate augmented versions
        for i in range(num_augmentations):
            aug_frames = frames.copy()
            
            # Apply temporal augmentation
            temp_aug = random.choice(self.temporal_augmentations)
            aug_frames = self.apply_temporal_augmentation(aug_frames, temp_aug)
            
            # Apply spatial augmentation to each frame
            spatial_aug_frames = []
            for frame in aug_frames:
                aug_frame = self.apply_spatial_augmentation(frame)
                spatial_aug_frames.append(aug_frame)
            
            # Convert to tensor format
            aug_frames_array = np.array(spatial_aug_frames, dtype=np.float32) / 255.0
            aug_frames_tensor = np.transpose(aug_frames_array, (0, 3, 1, 2))  # (frames, channels, height, width)
            
            augmented_videos.append(torch.FloatTensor(aug_frames_tensor))
        
        return augmented_videos

def augment_normal_videos():
    """Augment normal videos to balance the dataset"""
    print("🔄 Starting Data Augmentation for Normal Videos")
    print("=" * 60)
    
    # Paths
    normal_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video normale"
    suspicious_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video vol"
    
    # Count original videos
    normal_videos = [f for f in os.listdir(normal_dir) if f.lower().endswith(('.mp4', '.avi', '.mov'))]
    suspicious_videos = [f for f in os.listdir(suspicious_dir) if f.lower().endswith(('.mp4', '.avi', '.mov'))]
    
    print(f"📊 Original Dataset:")
    print(f"   Normal videos: {len(normal_videos)}")
    print(f"   Suspicious videos: {len(suspicious_videos)}")
    print(f"   Class imbalance: {len(suspicious_videos)/len(normal_videos):.2f}:1 (Suspicious:Normal)")
    
    # Calculate how many augmentations we need
    target_normal_count = len(suspicious_videos)  # Balance the classes
    augmentations_needed = max(0, target_normal_count - len(normal_videos))
    augmentations_per_video = max(1, augmentations_needed // len(normal_videos))
    
    print(f"\n🎯 Augmentation Plan:")
    print(f"   Target normal videos: {target_normal_count}")
    print(f"   Augmentations needed: {augmentations_needed}")
    print(f"   Augmentations per video: {augmentations_per_video}")
    
    # Initialize augmentation
    augmenter = VideoAugmentation()
    
    # Store augmented data
    augmented_data = []
    augmented_labels = []
    
    print(f"\n🔄 Generating augmented normal videos...")
    
    for video_file in tqdm(normal_videos, desc="Augmenting normal videos"):
        video_path = os.path.join(normal_dir, video_file)
        
        # Generate augmented versions
        aug_videos = augmenter.augment_video(video_path, num_augmentations=augmentations_per_video)
        
        for aug_video in aug_videos:
            augmented_data.append(aug_video)
            augmented_labels.append(0)  # Normal class
    
    print(f"\n✅ Augmentation completed!")
    print(f"   Generated {len(augmented_data)} augmented normal videos")
    
    # Save augmented data
    augmented_dataset = {
        'videos': augmented_data,
        'labels': augmented_labels,
        'original_normal_count': len(normal_videos),
        'original_suspicious_count': len(suspicious_videos),
        'augmented_normal_count': len(augmented_data),
        'augmentations_per_video': augmentations_per_video
    }
    
    torch.save(augmented_dataset, 'augmented_normal_videos.pth')
    print(f"💾 Augmented dataset saved to 'augmented_normal_videos.pth'")
    
    # Final statistics
    total_normal = len(normal_videos) + len(augmented_data)
    total_suspicious = len(suspicious_videos)
    
    print(f"\n📊 Final Dataset Statistics:")
    print(f"   Total normal videos: {total_normal} (original: {len(normal_videos)}, augmented: {len(augmented_data)})")
    print(f"   Total suspicious videos: {total_suspicious}")
    print(f"   New class balance: {total_suspicious/total_normal:.2f}:1 (Suspicious:Normal)")
    print(f"   Total dataset size: {total_normal + total_suspicious} videos")
    
    return augmented_dataset

if __name__ == "__main__":
    # Install required package if not available
    try:
        import albumentations
    except ImportError:
        print("Installing albumentations...")
        os.system("pip install albumentations")
        import albumentations as A
        from albumentations.pytorch import ToTensorV2
    
    augmented_dataset = augment_normal_videos()
