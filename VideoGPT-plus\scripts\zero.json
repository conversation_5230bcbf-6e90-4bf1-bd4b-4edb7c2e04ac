{"fp16": {"enabled": "auto", "loss_scale": 0, "loss_scale_window": 1000, "initial_scale_power": 16, "hysteresis": 2, "min_loss_scale": 1}, "bf16": {"enabled": "auto"}, "train_micro_batch_size_per_gpu": "auto", "train_batch_size": "auto", "gradient_accumulation_steps": "auto", "zero_optimization": {"stage": 1, "overlap_comm": true, "contiguous_gradients": true, "sub_group_size": 1000000000.0, "reduce_bucket_size": "auto"}}