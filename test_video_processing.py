#!/usr/bin/env python3
"""
Test script for video processing with VideoGPT+ setup
This script tests the video loading and processing functions
"""

import os
import sys
import torch
import cv2
import numpy as np
import glob
from pathlib import Path

def load_video_frames(video_path, max_frames=16, target_size=(224, 224)):
    """
    Load video frames from a video file
    
    Args:
        video_path: Path to video file
        max_frames: Maximum number of frames to extract
        target_size: Target size for frames (width, height)
    
    Returns:
        frames: Tensor of shape (T, C, H, W)
    """
    cap = cv2.VideoCapture(video_path)
    frames = []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Calculate frame indices to sample uniformly
    if total_frames <= max_frames:
        frame_indices = list(range(total_frames))
    else:
        frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
    
    for i, frame_idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if ret:
            # Convert BGR to RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # Resize frame
            frame = cv2.resize(frame, target_size)
            # Convert to tensor and normalize
            frame = torch.from_numpy(frame).float() / 255.0
            # Change from HWC to CHW
            frame = frame.permute(2, 0, 1)
            frames.append(frame)
    
    cap.release()
    
    if len(frames) == 0:
        raise ValueError(f"No frames could be loaded from {video_path}")
    
    # Stack frames into tensor (T, C, H, W)
    frames_tensor = torch.stack(frames)
    
    return frames_tensor

def scan_video_datasets(dataset_paths):
    """
    Scan video datasets and return information about available videos
    """
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    video_info = {}
    
    for dataset_name, dataset_path in dataset_paths.items():
        print(f"\nScanning dataset: {dataset_name}")
        print(f"Path: {dataset_path}")
        
        if not os.path.exists(dataset_path):
            print(f"Warning: Path does not exist: {dataset_path}")
            video_info[dataset_name] = {'videos': [], 'count': 0, 'path': dataset_path}
            continue
        
        videos = []
        for ext in video_extensions:
            pattern = os.path.join(dataset_path, f"**/*{ext}")
            videos.extend(glob.glob(pattern, recursive=True))
        
        video_info[dataset_name] = {
            'videos': videos,
            'count': len(videos),
            'path': dataset_path
        }
        
        print(f"Found {len(videos)} videos")
        if len(videos) > 0:
            print("Sample videos:")
            for i, video in enumerate(videos[:3]):
                print(f"  {i+1}. {os.path.basename(video)}")
            if len(videos) > 3:
                print(f"  ... and {len(videos) - 3} more")
    
    return video_info

def test_video_processing():
    """
    Test the video processing pipeline
    """
    print("Testing Video Processing Pipeline")
    print("=" * 50)
    
    # Define dataset paths
    DATASET_PATHS = {
        'video_normale': r'C:\Users\<USER>\Desktop\video pour mohamed\video normale',
        'video_vol': r'C:\Users\<USER>\Desktop\video pour mohamed\video vol'
    }
    
    # Scan datasets
    video_info = scan_video_datasets(DATASET_PATHS)
    
    # Test video loading on first available video
    for dataset_name, info in video_info.items():
        if info['count'] > 0:
            test_video = info['videos'][0]
            print(f"\nTesting video loading with: {os.path.basename(test_video)}")
            
            try:
                frames = load_video_frames(test_video, max_frames=8)
                print(f"✅ Successfully loaded video!")
                print(f"   Shape: {frames.shape}")
                print(f"   Data type: {frames.dtype}")
                print(f"   Value range: [{frames.min():.3f}, {frames.max():.3f}]")
                
                # Basic analysis
                mean_brightness = float(frames.mean())
                std_brightness = float(frames.std())
                print(f"   Mean brightness: {mean_brightness:.3f}")
                print(f"   Std brightness: {std_brightness:.3f}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error loading video: {e}")
                continue
    
    print("❌ No videos could be processed")
    return False

if __name__ == "__main__":
    print("VideoGPT+ Video Processing Test")
    print("=" * 40)
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"OpenCV version: {cv2.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    success = test_video_processing()
    
    if success:
        print("\n✅ Video processing test completed successfully!")
        print("You can now run the Jupyter notebook: VideoGPT_Plus_Custom_Dataset.ipynb")
    else:
        print("\n❌ Video processing test failed!")
        print("Please check your video file paths and formats.")
