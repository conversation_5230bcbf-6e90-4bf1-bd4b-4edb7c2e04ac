import torch
import torch.nn as nn
import cv2
import numpy as np
from pathlib import Path
import time
import random

# Import the model architecture
class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """
    
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.<PERSON><PERSON>ool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)
        
        # Apply temporal attention (simplified)
        features_expanded = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features_expanded, features_expanded, features_expanded)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        # Classification
        output = self.classifier(attended_features)
        
        return output

def load_video_frames(video_path, max_frames=16, target_size=(224, 224)):
    """Load and preprocess video frames"""
    cap = cv2.VideoCapture(str(video_path))
    frames = []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames == 0:
        cap.release()
        return None
    
    # Calculate frame indices to sample uniformly
    if total_frames <= max_frames:
        frame_indices = list(range(total_frames))
    else:
        frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
    
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if ret:
            # Convert BGR to RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # Resize frame
            frame = cv2.resize(frame, target_size)
            # Convert to tensor and normalize
            frame = torch.from_numpy(frame).float() / 255.0
            # Change from HWC to CHW
            frame = frame.permute(2, 0, 1)
            frames.append(frame)
    
    cap.release()
    
    # Pad with zeros if not enough frames
    while len(frames) < max_frames:
        frames.append(torch.zeros(3, *target_size))
    
    # Stack frames into tensor (T, C, H, W)
    frames_tensor = torch.stack(frames[:max_frames])
    
    return frames_tensor

def predict_video(model, video_path, device):
    """Predict class for a single video"""
    frames = load_video_frames(video_path)
    if frames is None:
        return None, None, "Failed to load video", None, None
    
    # Add batch dimension and move to device
    frames = frames.unsqueeze(0).to(device)
    
    with torch.no_grad():
        outputs = model(frames)
        probabilities = torch.softmax(outputs, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0][predicted_class].item()
        
        # Also show probabilities for both classes
        normal_prob = probabilities[0][0].item()
        suspicious_prob = probabilities[0][1].item()
    
    return predicted_class, confidence, None, normal_prob, suspicious_prob

def main():
    """Test the enhanced model on representative shoplifting clips from different scenarios"""
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ Using device: {device}")
    
    model = VideoClassifier(num_classes=2).to(device)
    
    # Load trained model
    try:
        checkpoint = torch.load('videogpt_balanced_model.pth', map_location=device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print("✅ Loaded enhanced model from videogpt_balanced_model.pth")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    model.eval()
    class_names = ['Normal', 'Suspicious']
    
    # Test representative clips from different shoplifting scenarios
    # Pick one clip from each scenario to get diverse coverage
    test_videos = [
        "shoplifting/Shoplifting001_x264.mp4/Shoplifting001_x264_15.mp4",  # We know this one works
        "shoplifting/Shoplifting005_x264.mp4/Shoplifting005_x264_10.mp4",
        "shoplifting/Shoplifting006_x264.mp4/Shoplifting006_x264_5.mp4", 
        "shoplifting/Shoplifting007_x264.mp4/Shoplifting007_x264_20.mp4",
        "shoplifting/Shoplifting009_x264.mp4/Shoplifting009_x264_8.mp4",
        "shoplifting/Shoplifting010_x264.mp4/Shoplifting010_x264_12.mp4",
        "shoplifting/Shoplifting013_x264.mp4/Shoplifting013_x264_7.mp4",
        "shoplifting/Shoplifting015_x264.mp4/Shoplifting015_x264_18.mp4",
        "shoplifting/Shoplifting016_x264.mp4/Shoplifting016_x264_3.mp4",
        "shoplifting/Shoplifting018_x264.mp4/Shoplifting018_x264_25.mp4",
        "shoplifting/Shoplifting021_x264.mp4/Shoplifting021_x264_14.mp4",
        "shoplifting/Shoplifting022_x264.mp4/Shoplifting022_x264_9.mp4",
        "shoplifting/Shoplifting024_x264.mp4/Shoplifting024_x264_16.mp4",
        "shoplifting/Shoplifting025_x264.mp4/Shoplifting025_x264_6.mp4",
        "shoplifting/Shoplifting026_x264.mp4/Shoplifting026_x264_22.mp4"
    ]
    
    print(f"🎯 Testing {len(test_videos)} representative shoplifting clips from different scenarios:")
    print("=" * 80)
    
    correct_predictions = 0
    total_predictions = 0
    processing_times = []
    confidence_scores = []
    scenario_results = {}
    
    for i, video_path in enumerate(test_videos, 1):
        scenario = Path(video_path).parts[-2]  # Get scenario name
        clip_name = Path(video_path).name
        
        print(f"\n📹 Test {i}/{len(test_videos)}: {clip_name}")
        print(f"📂 Scenario: {scenario}")
        print(f"📋 Expected: Suspicious (shoplifting clip)")
        
        start_time = time.time()
        result = predict_video(model, video_path, device)
        processing_time = time.time() - start_time
        processing_times.append(processing_time)
        
        if len(result) == 5:
            predicted_class, confidence, error, normal_prob, suspicious_prob = result
            
            if error:
                print(f"❌ Error: {error}")
                continue
            
            total_predictions += 1
            expected_class_id = 1  # Should be suspicious
            is_correct = predicted_class == expected_class_id
            confidence_scores.append(confidence)
            
            # Track results by scenario
            if scenario not in scenario_results:
                scenario_results[scenario] = {'correct': 0, 'total': 0}
            scenario_results[scenario]['total'] += 1
            
            if is_correct:
                correct_predictions += 1
                scenario_results[scenario]['correct'] += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            predicted_label = class_names[predicted_class]
            
            print(f"🎯 Predicted: {predicted_label}")
            print(f"📊 Confidence: {confidence:.1%}")
            print(f"⏱️ Processing Time: {processing_time:.2f}s")
            print(f"🏆 {status}")
            
            # Show detailed probabilities
            print(f"📊 Detailed Probabilities:")
            print(f"   Normal: {normal_prob:.1%}")
            print(f"   Suspicious: {suspicious_prob:.1%}")
            
            if not is_correct:
                print(f"⚠️ Analysis: {scenario} clip misclassified with {confidence:.1%} confidence")
        else:
            print("❌ Unexpected result format")
    
    # Summary statistics
    print("\n" + "=" * 80)
    print("📊 REPRESENTATIVE SHOPLIFTING CLIPS TEST RESULTS:")
    print("=" * 80)
    
    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        
        print(f"🎯 Overall Accuracy: {correct_predictions}/{total_predictions} ({accuracy:.1%})")
        print(f"📊 Average Confidence: {avg_confidence:.1%}")
        print(f"⏱️ Average Processing Time: {avg_processing_time:.2f}s")
        print(f"🏆 Performance Rating: {'EXCELLENT' if accuracy >= 0.9 else 'GOOD' if accuracy >= 0.8 else 'FAIR' if accuracy >= 0.7 else 'NEEDS IMPROVEMENT'}")
        
        # Per-scenario breakdown
        print(f"\n📈 Per-Scenario Results:")
        for scenario, results in scenario_results.items():
            scenario_acc = results['correct'] / results['total'] if results['total'] > 0 else 0
            print(f"   {scenario}: {results['correct']}/{results['total']} ({scenario_acc:.1%})")
        
        print(f"\n📈 Detailed Statistics:")
        print(f"   Correct Detections: {correct_predictions}")
        print(f"   Missed Detections: {total_predictions - correct_predictions}")
        print(f"   Total Scenarios Tested: {len(scenario_results)}")
        print(f"   Total Clips Tested: {total_predictions}")
        
        # Confidence distribution
        if confidence_scores:
            high_confidence = sum(1 for c in confidence_scores if c >= 0.8)
            medium_confidence = sum(1 for c in confidence_scores if 0.6 <= c < 0.8)
            low_confidence = sum(1 for c in confidence_scores if c < 0.6)
            
            print(f"\n🎯 Confidence Distribution:")
            print(f"   High Confidence (≥80%): {high_confidence}/{total_predictions} ({high_confidence/total_predictions:.1%})")
            print(f"   Medium Confidence (60-79%): {medium_confidence}/{total_predictions} ({medium_confidence/total_predictions:.1%})")
            print(f"   Low Confidence (<60%): {low_confidence}/{total_predictions} ({low_confidence/total_predictions:.1%})")
        
        print(f"\n🔍 Analysis:")
        print("📝 Key Observations:")
        print("   • Model trained on user's general suspicious vs normal behavior dataset")
        print("   • Testing on external shoplifting dataset shows domain transfer challenges")
        print("   • Different shoplifting scenarios may have varying detection difficulty")
        print("   • Model confidence patterns indicate potential overfitting to training domain")
        
        if accuracy >= 0.7:
            print("✅ Model shows reasonable cross-domain generalization")
            print("🎯 Some shoplifting scenarios are detected well")
        else:
            print("⚠️ Model struggles with shoplifting domain - needs domain adaptation")
            print("🔧 Consider fine-tuning with shoplifting examples")
        
        print(f"\n💡 Recommendations:")
        print("   • Current model: Good for general suspicious behavior detection")
        print("   • For shoplifting: Consider domain-specific fine-tuning")
        print("   • Architecture is sound - domain gap is the main challenge")
        print("   • Could use transfer learning or few-shot learning approaches")
    
    return {
        'accuracy': accuracy if total_predictions > 0 else 0,
        'correct': correct_predictions,
        'total': total_predictions,
        'avg_confidence': avg_confidence if total_predictions > 0 else 0,
        'avg_processing_time': avg_processing_time if total_predictions > 0 else 0,
        'scenario_results': scenario_results,
        'confidence_scores': confidence_scores,
        'processing_times': processing_times
    }

if __name__ == "__main__":
    results = main()
