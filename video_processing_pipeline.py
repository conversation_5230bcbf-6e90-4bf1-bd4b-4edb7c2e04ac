#!/usr/bin/env python3
"""
Comprehensive Video Processing Pipeline for VideoGPT+
This script provides batch processing capabilities for video datasets
"""

import os
import sys
import torch
import cv2
import numpy as np
import glob
import json
from pathlib import Path
from tqdm import tqdm
import argparse
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoProcessor:
    """
    Video processing pipeline for VideoGPT+ datasets
    """
    
    def __init__(self, max_frames=16, target_size=(224, 224), device='cpu'):
        self.max_frames = max_frames
        self.target_size = target_size
        self.device = device
        self.video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        
    def load_video_frames(self, video_path):
        """Load and preprocess video frames"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        # Calculate frame indices to sample uniformly
        if total_frames <= self.max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)
        
        cap.release()
        
        if len(frames) == 0:
            raise ValueError(f"No frames could be loaded from {video_path}")
        
        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames)
        
        return frames_tensor, {
            'total_frames': total_frames,
            'fps': fps,
            'duration': duration,
            'extracted_frames': len(frames)
        }
    
    def analyze_video(self, video_path):
        """Comprehensive video analysis"""
        try:
            frames, metadata = self.load_video_frames(video_path)
            
            # Basic statistics
            analysis = {
                'video_path': video_path,
                'filename': os.path.basename(video_path),
                'file_size': os.path.getsize(video_path),
                'metadata': metadata,
                'frame_analysis': {
                    'shape': list(frames.shape),
                    'mean_brightness': float(frames.mean()),
                    'std_brightness': float(frames.std()),
                    'min_value': float(frames.min()),
                    'max_value': float(frames.max())
                }
            }
            
            # Motion analysis (frame differences)
            frame_differences = []
            for i in range(1, frames.shape[0]):
                diff = torch.mean(torch.abs(frames[i] - frames[i-1]))
                frame_differences.append(float(diff))
            
            analysis['motion_analysis'] = {
                'frame_differences': frame_differences,
                'avg_motion': np.mean(frame_differences) if frame_differences else 0,
                'max_motion': np.max(frame_differences) if frame_differences else 0,
                'motion_variance': np.var(frame_differences) if frame_differences else 0
            }
            
            # Color analysis
            rgb_means = [float(frames[:, i].mean()) for i in range(3)]
            analysis['color_analysis'] = {
                'rgb_means': rgb_means,
                'dominant_channel': ['red', 'green', 'blue'][np.argmax(rgb_means)]
            }
            
            return analysis
            
        except Exception as e:
            return {
                'video_path': video_path,
                'filename': os.path.basename(video_path),
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def scan_dataset(self, dataset_path):
        """Scan dataset for video files"""
        videos = []
        if os.path.exists(dataset_path):
            for ext in self.video_extensions:
                pattern = os.path.join(dataset_path, f"**/*{ext}")
                videos.extend(glob.glob(pattern, recursive=True))
        return videos
    
    def process_dataset(self, dataset_path, dataset_name, output_dir=None, max_videos=None):
        """Process entire dataset"""
        logger.info(f"Processing dataset: {dataset_name}")
        logger.info(f"Dataset path: {dataset_path}")
        
        videos = self.scan_dataset(dataset_path)
        logger.info(f"Found {len(videos)} videos")
        
        if max_videos:
            videos = videos[:max_videos]
            logger.info(f"Processing first {len(videos)} videos")
        
        results = []
        successful = 0
        failed = 0
        
        for video_path in tqdm(videos, desc=f"Processing {dataset_name}"):
            analysis = self.analyze_video(video_path)
            results.append(analysis)
            
            if 'error' in analysis:
                failed += 1
                logger.warning(f"Failed to process {analysis['filename']}: {analysis['error']}")
            else:
                successful += 1
        
        # Summary statistics
        summary = {
            'dataset_name': dataset_name,
            'dataset_path': dataset_path,
            'total_videos': len(videos),
            'successful': successful,
            'failed': failed,
            'processing_date': datetime.now().isoformat(),
            'results': results
        }
        
        # Save results if output directory specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{dataset_name}_analysis.json")
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2)
            logger.info(f"Results saved to {output_file}")
        
        return summary
    
    def batch_process(self, dataset_configs, output_dir="results", max_videos_per_dataset=None):
        """Process multiple datasets"""
        all_results = {}
        
        for dataset_name, dataset_path in dataset_configs.items():
            results = self.process_dataset(
                dataset_path, 
                dataset_name, 
                output_dir, 
                max_videos_per_dataset
            )
            all_results[dataset_name] = results
        
        # Generate combined report
        self.generate_comparison_report(all_results, output_dir)
        
        return all_results
    
    def generate_comparison_report(self, all_results, output_dir):
        """Generate comparison report between datasets"""
        report_path = os.path.join(output_dir, "comparison_report.txt")
        
        with open(report_path, 'w') as f:
            f.write("VIDEO DATASET COMPARISON REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for dataset_name, results in all_results.items():
                f.write(f"Dataset: {dataset_name.upper()}\n")
                f.write("-" * 30 + "\n")
                f.write(f"Total videos: {results['total_videos']}\n")
                f.write(f"Successful: {results['successful']}\n")
                f.write(f"Failed: {results['failed']}\n")
                
                if results['successful'] > 0:
                    successful_results = [r for r in results['results'] if 'error' not in r]
                    
                    avg_brightness = np.mean([r['frame_analysis']['mean_brightness'] for r in successful_results])
                    avg_motion = np.mean([r['motion_analysis']['avg_motion'] for r in successful_results])
                    avg_duration = np.mean([r['metadata']['duration'] for r in successful_results])
                    
                    f.write(f"Average brightness: {avg_brightness:.3f}\n")
                    f.write(f"Average motion: {avg_motion:.3f}\n")
                    f.write(f"Average duration: {avg_duration:.1f}s\n")
                
                f.write("\n")
        
        logger.info(f"Comparison report saved to {report_path}")

def main():
    parser = argparse.ArgumentParser(description="Video Processing Pipeline for VideoGPT+")
    parser.add_argument("--max-frames", type=int, default=16, help="Maximum frames per video")
    parser.add_argument("--max-videos", type=int, help="Maximum videos per dataset")
    parser.add_argument("--output-dir", default="results", help="Output directory for results")
    parser.add_argument("--device", default="cpu", help="Device to use (cpu/cuda)")
    
    args = parser.parse_args()
    
    # Dataset configuration
    dataset_configs = {
        'video_normale': r'C:\Users\<USER>\Desktop\video pour mohamed\video normale',
        'video_vol': r'C:\Users\<USER>\Desktop\video pour mohamed\video vol'
    }
    
    # Initialize processor
    processor = VideoProcessor(
        max_frames=args.max_frames,
        device=args.device
    )
    
    # Process datasets
    logger.info("Starting video processing pipeline...")
    results = processor.batch_process(
        dataset_configs, 
        args.output_dir, 
        args.max_videos
    )
    
    logger.info("Processing completed!")
    logger.info(f"Results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
