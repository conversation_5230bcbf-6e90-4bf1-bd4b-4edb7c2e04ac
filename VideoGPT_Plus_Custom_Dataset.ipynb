{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"machine_shape": "hm", "gpuType": "T4", "provenance": []}, "accelerator": "GPU", "kaggle": {"accelerator": "gpu"}, "language_info": {"name": "python"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "markdown", "source": ["# VideoGPT+ Setup and Custom Dataset Processing\n", "\n", "This notebook sets up VideoGPT+ model and processes custom video datasets:\n", "- **Dataset 1**: `C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale`\n", "- **Dataset 2**: `C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video vol`\n", "\n", "VideoGPT+ integrates image and video encoders for enhanced video understanding.\n", "Repository: https://github.com/mbzuai-oryx/VideoGPT-plus"], "metadata": {"id": "VkrT__BFZDrI"}}, {"cell_type": "markdown", "source": ["## Step 1: Environment Setup\n", "Install required dependencies for VideoGPT+"], "metadata": {"id": "setup_header"}}, {"cell_type": "code", "source": ["# Install PyTorch with CUDA support\n", "!pip install torch==2.1.2 torchvision==0.16.2 --index-url https://download.pytorch.org/whl/cu118\n", "!pip install transformers==4.41.0"], "metadata": {"id": "install_torch"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Install VideoGPT+ requirements\n", "import os\n", "os.ch<PERSON>('VideoGPT-plus')\n", "!pip install -r requirements.txt"], "metadata": {"id": "install_requirements"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Install additional dependencies\n", "!pip install opencv-python\n", "!pip install matplotlib\n", "!pip install tqdm\n", "!pip install Pillow\n", "!pip install decord"], "metadata": {"id": "install_additional"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Setup VideoGPT+ Environment"], "metadata": {"id": "setup_env_header"}}, {"cell_type": "code", "source": ["# Setup Python path and imports\n", "import sys\n", "import os\n", "\n", "# Add VideoGPT-plus to Python path\n", "videogpt_path = os.path.abspath('VideoGPT-plus')\n", "if videogpt_path not in sys.path:\n", "    sys.path.insert(0, videogpt_path)\n", "\n", "# Set environment variable\n", "os.environ['PYTHONPATH'] = f\"{videogpt_path}:{os.environ.get('PYTHONPATH', '')}\"\n", "\n", "print(f\"VideoGPT+ path added: {videogpt_path}\")\n", "print(f\"Current working directory: {os.getcwd()}\")"], "metadata": {"id": "setup_path"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 3: Import Required Libraries"], "metadata": {"id": "import_header"}}, {"cell_type": "code", "source": ["import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "import glob\n", "from tqdm import tqdm\n", "import json\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA device: {torch.cuda.get_device_name(0)}\")"], "metadata": {"id": "import_libs"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 4: Video Processing Functions"], "metadata": {"id": "video_processing_header"}}, {"cell_type": "code", "source": ["def load_video_frames(video_path, max_frames=16, target_size=(224, 224)):\n", "    \"\"\"\n", "    Load video frames from a video file\n", "    \n", "    Args:\n", "        video_path: Path to video file\n", "        max_frames: Maximum number of frames to extract\n", "        target_size: Target size for frames (width, height)\n", "    \n", "    Returns:\n", "        frames: Tensor of shape (T, C, H, W)\n", "    \"\"\"\n", "    cap = cv2.VideoCapture(video_path)\n", "    frames = []\n", "    \n", "    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))\n", "    \n", "    # Calculate frame indices to sample uniformly\n", "    if total_frames <= max_frames:\n", "        frame_indices = list(range(total_frames))\n", "    else:\n", "        frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)\n", "    \n", "    for i, frame_idx in enumerate(frame_indices):\n", "        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)\n", "        ret, frame = cap.read()\n", "        \n", "        if ret:\n", "            # Convert BGR to RGB\n", "            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "            # Resize frame\n", "            frame = cv2.resize(frame, target_size)\n", "            # Convert to tensor and normalize\n", "            frame = torch.from_numpy(frame).float() / 255.0\n", "            # Change from HWC to CHW\n", "            frame = frame.permute(2, 0, 1)\n", "            frames.append(frame)\n", "    \n", "    cap.release()\n", "    \n", "    if len(frames) == 0:\n", "        raise ValueError(f\"No frames could be loaded from {video_path}\")\n", "    \n", "    # Stack frames into tensor (T, C, H, W)\n", "    frames_tensor = torch.stack(frames)\n", "    \n", "    return frames_tensor"], "metadata": {"id": "load_video_function"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def scan_video_datasets(dataset_paths):\n", "    \"\"\"\n", "    Scan video datasets and return information about available videos\n", "    \n", "    Args:\n", "        dataset_paths: List of paths to video datasets\n", "    \n", "    Returns:\n", "        video_info: Dictionary with dataset information\n", "    \"\"\"\n", "    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']\n", "    video_info = {}\n", "    \n", "    for dataset_name, dataset_path in dataset_paths.items():\n", "        print(f\"\\nScanning dataset: {dataset_name}\")\n", "        print(f\"Path: {dataset_path}\")\n", "        \n", "        if not os.path.exists(dataset_path):\n", "            print(f\"Warning: Path does not exist: {dataset_path}\")\n", "            video_info[dataset_name] = {'videos': [], 'count': 0, 'path': dataset_path}\n", "            continue\n", "        \n", "        videos = []\n", "        for ext in video_extensions:\n", "            pattern = os.path.join(dataset_path, f\"**/*{ext}\")\n", "            videos.extend(glob.glob(pattern, recursive=True))\n", "        \n", "        video_info[dataset_name] = {\n", "            'videos': videos,\n", "            'count': len(videos),\n", "            'path': dataset_path\n", "        }\n", "        \n", "        print(f\"Found {len(videos)} videos\")\n", "        if len(videos) > 0:\n", "            print(\"Sample videos:\")\n", "            for i, video in enumerate(videos[:3]):\n", "                print(f\"  {i+1}. {os.path.basename(video)}\")\n", "            if len(videos) > 3:\n", "                print(f\"  ... and {len(videos) - 3} more\")\n", "    \n", "    return video_info"], "metadata": {"id": "scan_datasets_function"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 5: Dataset Configuration"], "metadata": {"id": "dataset_config_header"}}, {"cell_type": "code", "source": ["# Define your custom dataset paths\n", "DATASET_PATHS = {\n", "    'video_normale': r'C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale',\n", "    'video_vol': r'C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video vol'\n", "}\n", "\n", "# Scan the datasets\n", "video_info = scan_video_datasets(DATASET_PATHS)\n", "\n", "# Print summary\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"DATASET SUMMARY\")\n", "print(\"=\"*50)\n", "total_videos = sum(info['count'] for info in video_info.values())\n", "print(f\"Total videos found: {total_videos}\")\n", "for dataset_name, info in video_info.items():\n", "    print(f\"  {dataset_name}: {info['count']} videos\")"], "metadata": {"id": "dataset_config"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 6: VideoGPT+ Model Setup"], "metadata": {"id": "model_setup_header"}}, {"cell_type": "code", "source": ["# Try to import VideoGPT+ components\n", "try:\n", "    from videogpt_plus.model.builder import load_pretrained_model\n", "    from videogpt_plus.mm_utils import get_model_name_from_path\n", "    from videogpt_plus.conversation import conv_templates, SeparatorStyle\n", "    print(\"VideoGPT+ modules imported successfully\")\n", "except ImportError as e:\n", "    print(f\"Import error: {e}\")\n", "    print(\"Trying alternative import method...\")\n", "    \n", "    # Alternative: Direct model loading\n", "    try:\n", "        import transformers\n", "        from transformers import AutoTokenizer, AutoModelForCausalLM\n", "        print(\"Using transformers for model loading\")\n", "    except ImportError as e2:\n", "        print(f\"Alternative import also failed: {e2}\")"], "metadata": {"id": "model_import"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Model configuration\n", "MODEL_CONFIG = {\n", "    'model_path': 'MBZUAI/VideoGPT-plus_Phi3-mini-4k',  # Hugging Face model\n", "    'device': 'cuda' if torch.cuda.is_available() else 'cpu',\n", "    'load_8bit': False,\n", "    'load_4bit': False,\n", "    'device_map': 'auto'\n", "}\n", "\n", "print(f\"Model will be loaded on: {MODEL_CONFIG['device']}\")"], "metadata": {"id": "model_config"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 7: Simple Video Analysis Functions"], "metadata": {"id": "analysis_header"}}, {"cell_type": "code", "source": ["def analyze_video_basic(video_path):\n", "    \"\"\"\n", "    Basic video analysis without requiring the full VideoGPT+ model\n", "    \"\"\"\n", "    try:\n", "        # Load video frames\n", "        frames = load_video_frames(video_path, max_frames=16)\n", "        \n", "        # Basic analysis\n", "        analysis = {\n", "            'video_path': video_path,\n", "            'num_frames': frames.shape[0],\n", "            'frame_shape': frames.shape[1:],  # (C, H, W)\n", "            'mean_brightness': float(frames.mean()),\n", "            'std_brightness': float(frames.std()),\n", "            'frame_differences': []\n", "        }\n", "        \n", "        # Calculate frame-to-frame differences (motion estimation)\n", "        for i in range(1, frames.shape[0]):\n", "            diff = torch.mean(torch.abs(frames[i] - frames[i-1]))\n", "            analysis['frame_differences'].append(float(diff))\n", "        \n", "        analysis['avg_motion'] = np.mean(analysis['frame_differences']) if analysis['frame_differences'] else 0\n", "        \n", "        return analysis\n", "        \n", "    except Exception as e:\n", "        return {'error': str(e), 'video_path': video_path}"], "metadata": {"id": "basic_analysis"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def visualize_video_frames(video_path, num_frames_to_show=4):\n", "    \"\"\"\n", "    Visualize frames from a video\n", "    \"\"\"\n", "    try:\n", "        frames = load_video_frames(video_path, max_frames=16)\n", "        \n", "        # Select frames to show\n", "        frame_indices = np.linspace(0, frames.shape[0]-1, num_frames_to_show, dtype=int)\n", "        \n", "        fig, axes = plt.subplots(1, num_frames_to_show, figsize=(15, 4))\n", "        if num_frames_to_show == 1:\n", "            axes = [axes]\n", "        \n", "        for i, frame_idx in enumerate(frame_indices):\n", "            frame = frames[frame_idx].permute(1, 2, 0)  # CHW to HWC\n", "            axes[i].imshow(frame)\n", "            axes[i].set_title(f'Frame {frame_idx}')\n", "            axes[i].axis('off')\n", "        \n", "        plt.suptitle(f'Video: {os.path.basename(video_path)}')\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"Error visualizing video {video_path}: {e}\")"], "metadata": {"id": "visualize_frames"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 8: Process Your Custom Datasets"], "metadata": {"id": "process_datasets_header"}}, {"cell_type": "code", "source": ["def process_dataset(dataset_name, video_list, max_videos=5):\n", "    \"\"\"\n", "    Process videos from a dataset\n", "    \"\"\"\n", "    print(f\"\\nProcessing dataset: {dataset_name}\")\n", "    print(f\"Total videos: {len(video_list)}\")\n", "    \n", "    # Limit number of videos to process\n", "    videos_to_process = video_list[:max_videos]\n", "    \n", "    results = []\n", "    \n", "    for i, video_path in enumerate(tqdm(videos_to_process, desc=f\"Processing {dataset_name}\")):\n", "        print(f\"\\nProcessing video {i+1}/{len(videos_to_process)}: {os.path.basename(video_path)}\")\n", "        \n", "        # Basic analysis\n", "        analysis = analyze_video_basic(video_path)\n", "        results.append(analysis)\n", "        \n", "        if 'error' not in analysis:\n", "            print(f\"  Frames: {analysis['num_frames']}\")\n", "            print(f\"  Mean brightness: {analysis['mean_brightness']:.3f}\")\n", "            print(f\"  Average motion: {analysis['avg_motion']:.3f}\")\n", "            \n", "            # Visualize first video\n", "            if i == 0:\n", "                print(\"  Visualizing frames...\")\n", "                visualize_video_frames(video_path)\n", "        else:\n", "            print(f\"  Error: {analysis['error']}\")\n", "    \n", "    return results"], "metadata": {"id": "process_dataset_function"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Process both datasets\n", "all_results = {}\n", "\n", "for dataset_name, info in video_info.items():\n", "    if info['count'] > 0:\n", "        results = process_dataset(dataset_name, info['videos'], max_videos=3)\n", "        all_results[dataset_name] = results\n", "    else:\n", "        print(f\"\\nSkipping {dataset_name} - no videos found\")\n", "        all_results[dataset_name] = []"], "metadata": {"id": "process_all_datasets"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 9: Results Summary and Analysis"], "metadata": {"id": "results_header"}}, {"cell_type": "code", "source": ["# Generate summary report\n", "def generate_summary_report(all_results):\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"VIDEO PROCESSING SUMMARY REPORT\")\n", "    print(\"=\"*60)\n", "    \n", "    for dataset_name, results in all_results.items():\n", "        print(f\"\\nDataset: {dataset_name.upper()}\")\n", "        print(\"-\" * 40)\n", "        \n", "        if not results:\n", "            print(\"No videos processed\")\n", "            continue\n", "        \n", "        successful_results = [r for r in results if 'error' not in r]\n", "        failed_results = [r for r in results if 'error' in r]\n", "        \n", "        print(f\"Total videos processed: {len(results)}\")\n", "        print(f\"Successful: {len(successful_results)}\")\n", "        print(f\"Failed: {len(failed_results)}\")\n", "        \n", "        if successful_results:\n", "            avg_brightness = np.mean([r['mean_brightness'] for r in successful_results])\n", "            avg_motion = np.mean([r['avg_motion'] for r in successful_results])\n", "            avg_frames = np.mean([r['num_frames'] for r in successful_results])\n", "            \n", "            print(f\"Average brightness: {avg_brightness:.3f}\")\n", "            print(f\"Average motion: {avg_motion:.3f}\")\n", "            print(f\"Average frames per video: {avg_frames:.1f}\")\n", "        \n", "        if failed_results:\n", "            print(\"\\nFailed videos:\")\n", "            for r in failed_results:\n", "                print(f\"  - {os.path.basename(r['video_path'])}: {r['error']}\")\n", "\n", "generate_summary_report(all_results)"], "metadata": {"id": "summary_report"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 10: Advanced Processing (Optional)"], "metadata": {"id": "advanced_header"}}, {"cell_type": "code", "source": ["# Save results to JSON file\n", "def save_results_to_file(all_results, filename='video_analysis_results.json'):\n", "    \"\"\"\n", "    Save analysis results to a JSON file\n", "    \"\"\"\n", "    # Convert numpy types to Python types for JSON serialization\n", "    def convert_numpy_types(obj):\n", "        if isinstance(obj, np.ndarray):\n", "            return obj.tolist()\n", "        elif isinstance(obj, np.integer):\n", "            return int(obj)\n", "        elif isinstance(obj, np.floating):\n", "            return float(obj)\n", "        elif isinstance(obj, dict):\n", "            return {key: convert_numpy_types(value) for key, value in obj.items()}\n", "        elif isinstance(obj, list):\n", "            return [convert_numpy_types(item) for item in obj]\n", "        else:\n", "            return obj\n", "    \n", "    serializable_results = convert_numpy_types(all_results)\n", "    \n", "    with open(filename, 'w') as f:\n", "        json.dump(serializable_results, f, indent=2)\n", "    \n", "    print(f\"Results saved to {filename}\")\n", "\n", "# Save the results\n", "save_results_to_file(all_results)"], "metadata": {"id": "save_results"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Create a simple video comparison\n", "def compare_datasets(all_results):\n", "    \"\"\"\n", "    Compare characteristics between the two datasets\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"DATASET COMPARISON\")\n", "    print(\"=\"*50)\n", "    \n", "    dataset_stats = {}\n", "    \n", "    for dataset_name, results in all_results.items():\n", "        successful_results = [r for r in results if 'error' not in r]\n", "        \n", "        if successful_results:\n", "            stats = {\n", "                'count': len(successful_results),\n", "                'avg_brightness': np.mean([r['mean_brightness'] for r in successful_results]),\n", "                'avg_motion': np.mean([r['avg_motion'] for r in successful_results]),\n", "                'avg_frames': np.mean([r['num_frames'] for r in successful_results])\n", "            }\n", "            dataset_stats[dataset_name] = stats\n", "    \n", "    # Print comparison\n", "    if len(dataset_stats) >= 2:\n", "        datasets = list(dataset_stats.keys())\n", "        print(f\"Comparing {datasets[0]} vs {datasets[1]}:\")\n", "        print()\n", "        \n", "        for metric in ['avg_brightness', 'avg_motion', 'avg_frames']:\n", "            val1 = dataset_stats[datasets[0]][metric]\n", "            val2 = dataset_stats[datasets[1]][metric]\n", "            diff = abs(val1 - val2)\n", "            print(f\"{metric.replace('_', ' ').title()}:\")\n", "            print(f\"  {datasets[0]}: {val1:.3f}\")\n", "            print(f\"  {datasets[1]}: {val2:.3f}\")\n", "            print(f\"  Difference: {diff:.3f}\")\n", "            print()\n", "    \n", "    return dataset_stats\n", "\n", "# Compare the datasets\n", "comparison_stats = compare_datasets(all_results)"], "metadata": {"id": "compare_datasets"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 11: Next Steps and Recommendations\n", "\n", "### What this notebook accomplished:\n", "1. ✅ Set up VideoGPT+ environment\n", "2. ✅ Scanned your custom video datasets\n", "3. ✅ Processed videos with basic analysis\n", "4. ✅ Visualized sample frames\n", "5. ✅ Generated comparison reports\n", "\n", "### For advanced VideoGPT+ features:\n", "- **Video Question Answering**: Load the full VideoGPT+ model for conversational video analysis\n", "- **Fine-tuning**: Train the model on your specific video types\n", "- **Batch Processing**: Process larger numbers of videos efficiently\n", "\n", "### Troubleshooting:\n", "- If videos fail to load, check file formats and corruption\n", "- For memory issues, reduce `max_frames` or process fewer videos at once\n", "- For model loading issues, ensure CUDA drivers are properly installed\n"], "metadata": {"id": "next_steps"}}]}