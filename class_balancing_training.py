import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import cv2
import numpy as np
import os
from pathlib import Path
from tqdm import tqdm
import random
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import time

# Import the model architecture (same as used in finetune_videogpt.py)
class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """

    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()

        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),

            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),

            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),

            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )

        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)

        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )

    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]

        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)

        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)

        # Apply temporal attention (simplified)
        features = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features, features, features)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)

        # Classification
        output = self.classifier(attended_features)

        return output

class BalancedVideoDataset(Dataset):
    def __init__(self, use_augmented=True, max_frames=16, target_size=(224, 224)):
        self.videos = []
        self.labels = []
        self.max_frames = max_frames
        self.target_size = target_size

        # Load original videos from directories
        print("Loading original videos...")
        normal_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video normale")
        suspicious_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video vol")

        # Load normal videos
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
        normal_videos = []
        for ext in video_extensions:
            normal_videos.extend(list(normal_dir.glob(f"*{ext}")))

        # Load suspicious videos
        suspicious_videos = []
        for ext in video_extensions:
            suspicious_videos.extend(list(suspicious_dir.glob(f"*{ext}")))

        print(f"Found {len(normal_videos)} normal videos")
        print(f"Found {len(suspicious_videos)} suspicious videos")

        # Process original videos
        for video_path in normal_videos:
            frames = self.load_video_frames(video_path)
            if frames is not None:
                self.videos.append(frames)
                self.labels.append(0)  # Normal class

        for video_path in suspicious_videos:
            frames = self.load_video_frames(video_path)
            if frames is not None:
                self.videos.append(frames)
                self.labels.append(1)  # Suspicious class

        # Load augmented normal videos if requested
        if use_augmented:
            print("Loading augmented normal videos...")
            try:
                augmented_data = torch.load('augmented_normal_videos.pth')
                # Only add the additional augmented videos (not the original ones)
                num_original_normal = len(normal_videos)
                augmented_videos = augmented_data['videos'][num_original_normal:]
                augmented_labels = augmented_data['labels'][num_original_normal:]

                self.videos.extend(augmented_videos)
                self.labels.extend(augmented_labels)
                print(f"Added {len(augmented_videos)} augmented normal videos")
            except FileNotFoundError:
                print("Augmented dataset not found, using original dataset only")

        self.labels = torch.tensor(self.labels)

        # Print class distribution
        unique, counts = torch.unique(self.labels, return_counts=True)
        print(f"Class distribution: {dict(zip(unique.tolist(), counts.tolist()))}")

    def load_video_frames(self, video_path):
        """Load and preprocess video frames"""
        cap = cv2.VideoCapture(str(video_path))
        frames = []

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames == 0:
            cap.release()
            return None

        # Calculate frame indices to sample uniformly
        if total_frames <= self.max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)

        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()

            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)

        cap.release()

        # Pad with zeros if not enough frames
        while len(frames) < self.max_frames:
            frames.append(torch.zeros(3, *self.target_size))

        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames[:self.max_frames])

        return frames_tensor

    def __len__(self):
        return len(self.videos)

    def __getitem__(self, idx):
        if isinstance(self.videos[idx], torch.Tensor):
            return self.videos[idx], self.labels[idx]
        else:
            # Convert to tensor if not already
            video_tensor = torch.stack(self.videos[idx])
            return video_tensor, self.labels[idx]

def create_weighted_sampler(labels):
    """Create weighted sampler for balanced training"""
    class_counts = torch.bincount(labels)
    class_weights = 1.0 / class_counts.float()
    sample_weights = class_weights[labels]
    
    sampler = WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )
    
    return sampler

def train_with_class_balancing():
    """Train model with class balancing techniques"""
    print("Starting training with class balancing...")
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create balanced dataset
    dataset = BalancedVideoDataset(use_augmented=True)
    
    # Calculate class weights for weighted loss
    class_counts = torch.bincount(dataset.labels)
    total_samples = len(dataset.labels)
    class_weights = total_samples / (len(class_counts) * class_counts.float())
    class_weights = class_weights.to(device)
    
    print(f"Class weights: {class_weights}")
    
    # Create weighted sampler for balanced sampling
    sampler = create_weighted_sampler(dataset.labels)
    
    # Create data loader with weighted sampler
    dataloader = DataLoader(
        dataset, 
        batch_size=4, 
        sampler=sampler,
        num_workers=0
    )
    
    # Initialize model
    model = VideoClassifier(num_classes=2).to(device)
    
    # Load pretrained weights if available
    try:
        checkpoint = torch.load('videogpt_retrained_model.pth', map_location=device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print("Loaded pretrained model weights")
    except (FileNotFoundError, KeyError, RuntimeError) as e:
        print(f"Could not load pretrained weights: {e}")
        print("Training from scratch")
    
    # Weighted CrossEntropy loss
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    optimizer = optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=2, gamma=0.8)
    
    # Training parameters
    num_epochs = 5
    
    # Training loop
    model.train()
    for epoch in range(num_epochs):
        epoch_loss = 0.0
        epoch_correct = 0
        epoch_total = 0
        all_predictions = []
        all_labels = []
        
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        progress_bar = tqdm(dataloader, desc=f"Training")
        
        for batch_idx, (videos, labels) in enumerate(progress_bar):
            videos = videos.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(videos)
            loss = criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Statistics
            epoch_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            epoch_total += labels.size(0)
            epoch_correct += (predicted == labels).sum().item()
            
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100 * epoch_correct / epoch_total:.2f}%'
            })
        
        # Calculate epoch metrics
        epoch_accuracy = 100 * epoch_correct / epoch_total
        epoch_f1 = f1_score(all_labels, all_predictions, average='weighted')
        avg_loss = epoch_loss / len(dataloader)
        
        print(f"Epoch {epoch+1} Results:")
        print(f"  Average Loss: {avg_loss:.4f}")
        print(f"  Accuracy: {epoch_accuracy:.2f}%")
        print(f"  F1 Score: {epoch_f1:.4f}")
        
        # Print confusion matrix
        cm = confusion_matrix(all_labels, all_predictions)
        print(f"  Confusion Matrix:")
        print(f"    Normal: {cm[0]}")
        print(f"    Suspicious: {cm[1]}")
        
        # Step scheduler
        scheduler.step()
        print(f"  Learning Rate: {scheduler.get_last_lr()[0]:.6f}")
    
    # Save the balanced model
    torch.save(model.state_dict(), 'videogpt_balanced_model.pth')
    print("\nModel saved as 'videogpt_balanced_model.pth'")
    
    # Final evaluation on the entire dataset
    print("\nFinal evaluation on entire dataset:")
    model.eval()
    all_predictions = []
    all_labels = []
    
    # Create evaluation dataloader without sampling
    eval_dataloader = DataLoader(dataset, batch_size=4, shuffle=False, num_workers=0)
    
    with torch.no_grad():
        for videos, labels in eval_dataloader:
            videos = videos.to(device)
            labels = labels.to(device)
            
            outputs = model(videos)
            _, predicted = torch.max(outputs.data, 1)
            
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    final_accuracy = accuracy_score(all_labels, all_predictions)
    final_f1 = f1_score(all_labels, all_predictions, average='weighted')
    final_cm = confusion_matrix(all_labels, all_predictions)
    
    print(f"Final Results:")
    print(f"  Accuracy: {final_accuracy*100:.2f}%")
    print(f"  F1 Score: {final_f1:.4f}")
    print(f"  Confusion Matrix:")
    print(f"    Normal: {final_cm[0]}")
    print(f"    Suspicious: {final_cm[1]}")
    
    return model

if __name__ == "__main__":
    model = train_with_class_balancing()
