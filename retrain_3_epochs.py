import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import cv2
import numpy as np
import os
from pathlib import Path
import json
from tqdm import tqdm
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
import time

class VideoDataset(Dataset):
    def __init__(self, video_paths, labels, max_frames=16, target_size=(224, 224)):
        self.video_paths = video_paths
        self.labels = labels
        self.max_frames = max_frames
        self.target_size = target_size
    
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        
        # Load video frames
        frames = self.load_video_frames(video_path)
        
        return frames, label
    
    def load_video_frames(self, video_path):
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        # Get total frame count
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            # Create dummy frames if video can't be read
            frames = [np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8) 
                     for _ in range(self.max_frames)]
        else:
            # Calculate frame indices for uniform sampling
            if total_frames >= self.max_frames:
                frame_indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)
            else:
                frame_indices = np.arange(total_frames)
                while len(frame_indices) < self.max_frames:
                    frame_indices = np.concatenate([frame_indices, frame_indices])
                frame_indices = frame_indices[:self.max_frames]
            
            # Extract frames
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    frame = cv2.resize(frame, self.target_size)
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frames.append(frame)
                else:
                    # Use last valid frame if read fails
                    if frames:
                        frames.append(frames[-1])
                    else:
                        frames.append(np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8))
        
        cap.release()
        
        # Ensure we have exactly max_frames
        while len(frames) < self.max_frames:
            frames.append(frames[-1] if frames else np.zeros((self.target_size[1], self.target_size[0], 3), dtype=np.uint8))
        frames = frames[:self.max_frames]
        
        # Convert to tensor and normalize
        frames = np.array(frames, dtype=np.float32) / 255.0
        frames = np.transpose(frames, (0, 3, 1, 2))  # (frames, channels, height, width)
        
        return torch.FloatTensor(frames)

class VideoClassifier(nn.Module):
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.view(batch_size, -1)  # (batch_size, feature_dim)
        
        # Apply temporal attention
        features_att = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features_att, features_att, features_att)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        # Classification
        output = self.classifier(attended_features)
        return output

def prepare_dataset():
    """Prepare the dataset with proper labels"""
    print("Preparing dataset...")
    
    # Dataset paths
    normal_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video normale"
    suspicious_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video vol"
    
    video_paths = []
    labels = []
    
    # Load normal videos (label 0)
    if os.path.exists(normal_dir):
        normal_videos = [f for f in os.listdir(normal_dir) if f.lower().endswith(('.mp4', '.avi', '.mov'))]
        for video in normal_videos:
            video_paths.append(os.path.join(normal_dir, video))
            labels.append(0)  # Normal = 0
        print(f"Found {len(normal_videos)} normal videos")
    
    # Load suspicious videos (label 1)
    if os.path.exists(suspicious_dir):
        suspicious_videos = [f for f in os.listdir(suspicious_dir) if f.lower().endswith(('.mp4', '.avi', '.mov'))]
        for video in suspicious_videos:
            video_paths.append(os.path.join(suspicious_dir, video))
            labels.append(1)  # Suspicious = 1
        print(f"Found {len(suspicious_videos)} suspicious videos")
    
    print(f"Total dataset: {len(video_paths)} videos")
    print(f"Class distribution: Normal={labels.count(0)}, Suspicious={labels.count(1)}")
    
    return video_paths, labels

def retrain_model():
    """Retrain the model for 3 epochs with detailed metrics"""
    print("VideoGPT+ Retraining (3 Epochs)")
    print("=" * 50)
    
    # Prepare dataset
    video_paths, labels = prepare_dataset()
    
    # Split dataset
    train_paths, val_paths, train_labels, val_labels = train_test_split(
        video_paths, labels, test_size=0.2, random_state=42, stratify=labels
    )
    
    print(f"Training set: {len(train_paths)} videos")
    print(f"Validation set: {len(val_paths)} videos")
    
    # Create datasets and dataloaders
    train_dataset = VideoDataset(train_paths, train_labels)
    val_dataset = VideoDataset(val_paths, val_labels)
    
    train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=2, shuffle=False, num_workers=0)
    
    # Initialize model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    model = VideoClassifier(num_classes=2)
    
    # Load previous weights if available
    model_path = "videogpt_finetuned_model.pth"
    if os.path.exists(model_path):
        print("Loading previous model weights...")
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("Previous weights loaded successfully!")
    
    model.to(device)
    
    # Training setup
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=2, gamma=0.5)
    
    # Training history
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'val_f1': []
    }
    
    best_val_acc = 0.0
    
    # Training loop for 3 epochs
    for epoch in range(3):
        print(f"\nEpoch {epoch+1}/3")
        print("-" * 30)
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc="Training")
        for batch_idx, (videos, labels_batch) in enumerate(train_pbar):
            videos = videos.to(device)
            labels_batch = labels_batch.to(device)
            
            optimizer.zero_grad()
            outputs = model(videos)
            loss = criterion(outputs, labels_batch)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels_batch.size(0)
            train_correct += (predicted == labels_batch).sum().item()
            
            # Update progress bar
            current_acc = 100.0 * train_correct / train_total
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{current_acc:.2f}%'
            })
        
        train_loss /= len(train_loader)
        train_acc = 100.0 * train_correct / train_total
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc="Validation")
            for videos, labels_batch in val_pbar:
                videos = videos.to(device)
                labels_batch = labels_batch.to(device)
                
                outputs = model(videos)
                loss = criterion(outputs, labels_batch)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels_batch.size(0)
                val_correct += (predicted == labels_batch).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels_batch.cpu().numpy())
                
                # Update progress bar
                current_acc = 100.0 * val_correct / val_total
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{current_acc:.2f}%'
                })
        
        val_loss /= len(val_loader)
        val_acc = 100.0 * val_correct / val_total
        val_f1 = f1_score(all_labels, all_predictions, average='weighted')
        
        # Update learning rate
        scheduler.step()
        
        # Save history
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        history['val_f1'].append(val_f1)
        
        # Print epoch results
        print(f"\nEpoch {epoch+1} Results:")
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
        print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
        print(f"Val F1 Score: {val_f1:.4f}")
        print(f"Learning Rate: {scheduler.get_last_lr()[0]:.6f}")
        
        # Detailed classification report
        print("\nDetailed Classification Report:")
        class_names = ['Normal', 'Suspicious']
        print(classification_report(all_labels, all_predictions, target_names=class_names))
        
        # Confusion matrix
        cm = confusion_matrix(all_labels, all_predictions)
        print("Confusion Matrix:")
        print(f"                Predicted")
        print(f"            Normal  Suspicious")
        print(f"Actual Normal    {cm[0][0]:2d}        {cm[0][1]:2d}")
        print(f"   Suspicious    {cm[1][0]:2d}        {cm[1][1]:2d}")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_acc': best_val_acc / 100.0,
                'history': history
            }, 'videogpt_retrained_model.pth')
            print(f"✅ New best model saved! Validation accuracy: {val_acc:.2f}%")
        
        print("=" * 50)
    
    print(f"\nRetraining completed!")
    print(f"Best validation accuracy: {best_val_acc:.2f}%")
    
    # Save final history
    with open('retraining_history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    return history

if __name__ == "__main__":
    history = retrain_model()
