# VideoGPT+ Custom Dataset Processing

This repository contains a complete setup for processing custom video datasets with VideoGPT+, a state-of-the-art video understanding model.

## 📁 Repository Structure

```
videogpt/
├── VideoGPT-plus/                    # Cloned VideoGPT+ repository
├── VideoGPT_Plus_Custom_Dataset.ipynb # Main Jupyter notebook
├── video_processing_pipeline.py      # Batch processing script
├── test_video_processing.py          # Test script
└── README.md                         # This file
```

## 🎯 Your Custom Datasets

The setup is configured to work with your video datasets:
- **Dataset 1**: `C:\Users\<USER>\Desktop\video pour mohamed\video normale`
- **Dataset 2**: `C:\Users\<USER>\Desktop\video pour mohamed\video vol`

## 🚀 Quick Start

### Option 1: Jupyter Notebook (Recommended)
1. Open `VideoGPT_Plus_Custom_Dataset.ipynb` in Jupyter
2. Run all cells sequentially
3. The notebook will:
   - Install dependencies
   - Scan your video datasets
   - Process sample videos
   - Generate analysis reports

### Option 2: Command Line Processing
```bash
# Test video processing
python test_video_processing.py

# Run full batch processing
python video_processing_pipeline.py --max-videos 5 --output-dir results
```

## 📋 Features

### ✅ Completed Features
- **Environment Setup**: Automatic installation of VideoGPT+ dependencies
- **Video Loading**: Robust video frame extraction with error handling
- **Dataset Scanning**: Automatic discovery of video files in your directories
- **Basic Analysis**: Frame analysis, motion detection, brightness statistics
- **Visualization**: Sample frame display and comparison charts
- **Batch Processing**: Process multiple videos efficiently
- **Results Export**: JSON export and comparison reports

### 🔄 Processing Pipeline
1. **Video Discovery**: Scans directories for supported video formats
2. **Frame Extraction**: Extracts uniform frames from each video
3. **Preprocessing**: Resizes and normalizes frames for model input
4. **Analysis**: Computes motion, brightness, and color statistics
5. **Reporting**: Generates detailed analysis reports

## 🛠️ Technical Details

### Supported Video Formats
- MP4, AVI, MOV, MKV, WMV, FLV, WebM

### Processing Parameters
- **Max Frames**: 16 frames per video (configurable)
- **Frame Size**: 224x224 pixels (VideoGPT+ standard)
- **Sampling**: Uniform temporal sampling across video duration

### Dependencies
- PyTorch 2.1.2 with CUDA 11.8 support
- Transformers 4.41.0
- OpenCV for video processing
- Additional packages: numpy, matplotlib, tqdm, Pillow

## 📊 Output Examples

### Video Analysis Results
```json
{
  "filename": "sample_video.mp4",
  "metadata": {
    "total_frames": 300,
    "fps": 30.0,
    "duration": 10.0
  },
  "frame_analysis": {
    "mean_brightness": 0.456,
    "std_brightness": 0.123
  },
  "motion_analysis": {
    "avg_motion": 0.034,
    "max_motion": 0.089
  }
}
```

### Dataset Comparison
```
Dataset: VIDEO_NORMALE
- Total videos: 15
- Average brightness: 0.456
- Average motion: 0.034

Dataset: VIDEO_VOL  
- Total videos: 12
- Average brightness: 0.523
- Average motion: 0.067
```

## 🔧 Troubleshooting

### Common Issues

1. **"No videos found"**
   - Check that your dataset paths exist
   - Verify video file formats are supported
   - Ensure files aren't corrupted

2. **"CUDA out of memory"**
   - Reduce `max_frames` parameter
   - Process fewer videos at once
   - Use CPU instead of GPU

3. **"Module import errors"**
   - Run the dependency installation cells
   - Restart the Jupyter kernel
   - Check Python path configuration

### Performance Tips
- Use GPU if available for faster processing
- Process videos in batches to manage memory
- Consider reducing frame resolution for large datasets

## 📈 Next Steps

### Advanced VideoGPT+ Features
1. **Video Question Answering**: Load full model for conversational analysis
2. **Fine-tuning**: Train on your specific video types
3. **Real-time Processing**: Stream processing capabilities

### Model Loading (Future Enhancement)
```python
# Example for loading full VideoGPT+ model
from videogpt_plus.model.builder import load_pretrained_model

model_path = 'MBZUAI/VideoGPT-plus_Phi3-mini-4k'
tokenizer, model, image_processor, context_len = load_pretrained_model(
    model_path=model_path,
    model_base=None,
    model_name=get_model_name_from_path(model_path)
)
```

## 🤝 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the Jupyter notebook output for error messages
3. Ensure all dependencies are properly installed
4. Verify your video file paths and formats

## 📝 Notes

- The current setup focuses on video preprocessing and basic analysis
- Full VideoGPT+ model loading requires additional setup for conversational features
- All processing respects your original video files (read-only operations)
- Results are saved in JSON format for further analysis

---

**Ready to process your videos!** Start with the Jupyter notebook for an interactive experience.
