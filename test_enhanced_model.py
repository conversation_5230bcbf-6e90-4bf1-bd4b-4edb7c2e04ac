import torch
import torch.nn as nn
import cv2
import numpy as np
import os
from pathlib import Path
import random

# Import the model architecture
class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """
    
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.<PERSON>Pool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)
        
        # Apply temporal attention (simplified)
        features_expanded = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features_expanded, features_expanded, features_expanded)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        # Classification
        output = self.classifier(attended_features)
        
        return output

class VideoTester:
    def __init__(self, model_path='videogpt_balanced_model.pth', device=None):
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = VideoClassifier(num_classes=2).to(self.device)
        
        # Load trained model
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            print(f"✅ Loaded enhanced model from {model_path}")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return
        
        self.model.eval()
        self.class_names = ['Normal', 'Suspicious']
        
    def load_video_frames(self, video_path, max_frames=16, target_size=(224, 224)):
        """Load and preprocess video frames"""
        cap = cv2.VideoCapture(str(video_path))
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames == 0:
            cap.release()
            return None
        
        # Calculate frame indices to sample uniformly
        if total_frames <= max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)
        
        cap.release()
        
        # Pad with zeros if not enough frames
        while len(frames) < max_frames:
            frames.append(torch.zeros(3, *target_size))
        
        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames[:max_frames])
        
        return frames_tensor
    
    def predict_video(self, video_path):
        """Predict class for a single video"""
        frames = self.load_video_frames(video_path)
        if frames is None:
            return None, None, "Failed to load video"
        
        # Add batch dimension and move to device
        frames = frames.unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(frames)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class].item()
        
        return predicted_class, confidence, None
    
    def test_random_videos(self, num_normal=5, num_suspicious=5):
        """Test random videos from both classes"""
        print("🧪 Testing Enhanced VideoGPT+ Model")
        print("=" * 50)
        
        # Get video paths
        normal_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video normale")
        suspicious_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video vol")
        
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
        
        # Collect normal videos
        normal_videos = []
        for ext in video_extensions:
            normal_videos.extend(list(normal_dir.glob(f"*{ext}")))
        
        # Collect suspicious videos
        suspicious_videos = []
        for ext in video_extensions:
            suspicious_videos.extend(list(suspicious_dir.glob(f"*{ext}")))
        
        print(f"📁 Found {len(normal_videos)} normal videos")
        print(f"📁 Found {len(suspicious_videos)} suspicious videos")
        print()
        
        # Test normal videos
        print("🟢 Testing Normal Videos:")
        print("-" * 30)
        normal_correct = 0
        normal_tested = 0
        
        test_normal = random.sample(normal_videos, min(num_normal, len(normal_videos)))
        
        for i, video_path in enumerate(test_normal, 1):
            predicted_class, confidence, error = self.predict_video(video_path)
            
            if error:
                print(f"❌ Video {i}: {video_path.name} - Error: {error}")
                continue
            
            normal_tested += 1
            is_correct = predicted_class == 0  # 0 is normal class
            if is_correct:
                normal_correct += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            predicted_label = self.class_names[predicted_class]
            
            print(f"{status} Video {i}: {video_path.name}")
            print(f"   Predicted: {predicted_label} (Confidence: {confidence:.1%})")
            print()
        
        # Test suspicious videos
        print("🔴 Testing Suspicious Videos:")
        print("-" * 30)
        suspicious_correct = 0
        suspicious_tested = 0
        
        test_suspicious = random.sample(suspicious_videos, min(num_suspicious, len(suspicious_videos)))
        
        for i, video_path in enumerate(test_suspicious, 1):
            predicted_class, confidence, error = self.predict_video(video_path)
            
            if error:
                print(f"❌ Video {i}: {video_path.name} - Error: {error}")
                continue
            
            suspicious_tested += 1
            is_correct = predicted_class == 1  # 1 is suspicious class
            if is_correct:
                suspicious_correct += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            predicted_label = self.class_names[predicted_class]
            
            print(f"{status} Video {i}: {video_path.name}")
            print(f"   Predicted: {predicted_label} (Confidence: {confidence:.1%})")
            print()
        
        # Summary
        print("📊 Test Results Summary:")
        print("=" * 50)
        
        if normal_tested > 0:
            normal_accuracy = normal_correct / normal_tested
            print(f"🟢 Normal Videos: {normal_correct}/{normal_tested} correct ({normal_accuracy:.1%})")
        
        if suspicious_tested > 0:
            suspicious_accuracy = suspicious_correct / suspicious_tested
            print(f"🔴 Suspicious Videos: {suspicious_correct}/{suspicious_tested} correct ({suspicious_accuracy:.1%})")
        
        total_tested = normal_tested + suspicious_tested
        total_correct = normal_correct + suspicious_correct
        
        if total_tested > 0:
            overall_accuracy = total_correct / total_tested
            print(f"🎯 Overall Accuracy: {total_correct}/{total_tested} ({overall_accuracy:.1%})")
        
        print()
        print("🎉 Enhanced model testing completed!")
        
        return {
            'normal_accuracy': normal_correct / normal_tested if normal_tested > 0 else 0,
            'suspicious_accuracy': suspicious_correct / suspicious_tested if suspicious_tested > 0 else 0,
            'overall_accuracy': total_correct / total_tested if total_tested > 0 else 0,
            'normal_tested': normal_tested,
            'suspicious_tested': suspicious_tested,
            'normal_correct': normal_correct,
            'suspicious_correct': suspicious_correct
        }
    
    def test_specific_video(self, video_path):
        """Test a specific video"""
        print(f"🎬 Testing video: {Path(video_path).name}")
        
        predicted_class, confidence, error = self.predict_video(video_path)
        
        if error:
            print(f"❌ Error: {error}")
            return None
        
        predicted_label = self.class_names[predicted_class]
        print(f"📊 Prediction: {predicted_label}")
        print(f"🎯 Confidence: {confidence:.1%}")
        
        return {
            'predicted_class': predicted_class,
            'predicted_label': predicted_label,
            'confidence': confidence
        }

def main():
    """Main testing function"""
    tester = VideoTester()
    
    # Test random videos from both classes (larger sample)
    results = tester.test_random_videos(num_normal=8, num_suspicious=8)
    
    return results

if __name__ == "__main__":
    results = main()
