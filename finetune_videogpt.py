#!/usr/bin/env python3
"""
VideoGPT+ Fine-tuning Script
Fine-tunes VideoGPT+ model on custom video datasets (70 videos total)
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import cv2
import numpy as np
import json
import glob
from pathlib import Path
from tqdm import tqdm
import time
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score
import matplotlib.pyplot as plt

# Add VideoGPT-plus to path
videogpt_path = os.path.abspath('VideoGPT-plus')
if videogpt_path not in sys.path:
    sys.path.insert(0, videogpt_path)

class VideoDataset(Dataset):
    """
    Custom dataset for video classification
    """
    
    def __init__(self, video_paths, labels, max_frames=16, target_size=(224, 224), transform=None):
        self.video_paths = video_paths
        self.labels = labels
        self.max_frames = max_frames
        self.target_size = target_size
        self.transform = transform
        
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        
        try:
            frames = self.load_video_frames(video_path)
            return frames, torch.tensor(label, dtype=torch.long)
        except Exception as e:
            print(f"Error loading {video_path}: {e}")
            # Return dummy data if video fails to load
            dummy_frames = torch.zeros(self.max_frames, 3, *self.target_size)
            return dummy_frames, torch.tensor(label, dtype=torch.long)
    
    def load_video_frames(self, video_path):
        """Load and preprocess video frames"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Calculate frame indices to sample uniformly
        if total_frames <= self.max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)
        
        cap.release()
        
        # Pad with zeros if not enough frames
        while len(frames) < self.max_frames:
            frames.append(torch.zeros(3, *self.target_size))
        
        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames[:self.max_frames])
        
        return frames_tensor

class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """
    
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)
        
        # Apply temporal attention (simplified)
        features = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features, features, features)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        # Classification
        output = self.classifier(attended_features)
        
        return output

class VideoGPTFineTuner:
    """
    Fine-tuning manager for VideoGPT+
    """
    
    def __init__(self, max_frames=16, target_size=(224, 224), device=None):
        self.max_frames = max_frames
        self.target_size = target_size
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        
        print(f"Fine-tuner initialized on device: {self.device}")
        
    def prepare_dataset(self, dataset_configs):
        """Prepare dataset for training"""
        print("Preparing dataset...")
        
        all_video_paths = []
        all_labels = []
        
        label_mapping = {
            'video_normale': 0,  # Normal videos
            'video_vol': 1       # Vol videos
        }
        
        for dataset_name, dataset_path in dataset_configs.items():
            print(f"Scanning {dataset_name}: {dataset_path}")
            
            videos = []
            if os.path.exists(dataset_path):
                for ext in self.video_extensions:
                    pattern = os.path.join(dataset_path, f"**/*{ext}")
                    videos.extend(glob.glob(pattern, recursive=True))
            
            label = label_mapping[dataset_name]
            
            print(f"Found {len(videos)} videos in {dataset_name}")
            
            all_video_paths.extend(videos)
            all_labels.extend([label] * len(videos))
        
        print(f"Total dataset: {len(all_video_paths)} videos")
        print(f"Class distribution: Normal={all_labels.count(0)}, Vol={all_labels.count(1)}")
        
        return all_video_paths, all_labels
    
    def create_data_loaders(self, video_paths, labels, test_size=0.2, batch_size=4):
        """Create train and validation data loaders"""
        # Split dataset
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            video_paths, labels, test_size=test_size, random_state=42, stratify=labels
        )
        
        print(f"Training set: {len(train_paths)} videos")
        print(f"Validation set: {len(val_paths)} videos")
        
        # Create datasets
        train_dataset = VideoDataset(train_paths, train_labels, self.max_frames, self.target_size)
        val_dataset = VideoDataset(val_paths, val_labels, self.max_frames, self.target_size)
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
        
        return train_loader, val_loader
    
    def train_model(self, train_loader, val_loader, num_epochs=20, learning_rate=0.001):
        """Train the video classifier"""
        print(f"Starting training for {num_epochs} epochs...")
        
        # Initialize model
        model = VideoClassifier(num_classes=2, max_frames=self.max_frames).to(self.device)
        
        # Loss and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
        
        # Training history
        train_losses = []
        val_losses = []
        train_accuracies = []
        val_accuracies = []
        
        best_val_acc = 0.0
        best_model_state = None
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 30)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            train_pbar = tqdm(train_loader, desc="Training")
            for batch_idx, (videos, labels) in enumerate(train_pbar):
                videos = videos.to(self.device)
                labels = labels.to(self.device)
                
                optimizer.zero_grad()
                
                outputs = model(videos)
                loss = criterion(outputs, labels)
                
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100.*train_correct/train_total:.2f}%'
                })
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc="Validation")
                for videos, labels in val_pbar:
                    videos = videos.to(self.device)
                    labels = labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels.size(0)
                    val_correct += (predicted == labels).sum().item()
                    
                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'Acc': f'{100.*val_correct/val_total:.2f}%'
                    })
            
            # Calculate epoch metrics
            epoch_train_loss = train_loss / len(train_loader)
            epoch_val_loss = val_loss / len(val_loader)
            epoch_train_acc = 100. * train_correct / train_total
            epoch_val_acc = 100. * val_correct / val_total
            
            train_losses.append(epoch_train_loss)
            val_losses.append(epoch_val_loss)
            train_accuracies.append(epoch_train_acc)
            val_accuracies.append(epoch_val_acc)
            
            print(f"Train Loss: {epoch_train_loss:.4f}, Train Acc: {epoch_train_acc:.2f}%")
            print(f"Val Loss: {epoch_val_loss:.4f}, Val Acc: {epoch_val_acc:.2f}%")
            
            # Save best model
            if epoch_val_acc > best_val_acc:
                best_val_acc = epoch_val_acc
                best_model_state = model.state_dict().copy()
                print(f"New best validation accuracy: {best_val_acc:.2f}%")
            
            scheduler.step()
        
        # Load best model
        if best_model_state:
            model.load_state_dict(best_model_state)
        
        # Save model
        torch.save({
            'model_state_dict': model.state_dict(),
            'best_val_acc': best_val_acc,
            'training_history': {
                'train_losses': train_losses,
                'val_losses': val_losses,
                'train_accuracies': train_accuracies,
                'val_accuracies': val_accuracies
            }
        }, 'videogpt_finetuned_model.pth')
        
        print(f"\nTraining completed! Best validation accuracy: {best_val_acc:.2f}%")
        print("Model saved as: videogpt_finetuned_model.pth")
        
        return model, {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_accuracies': train_accuracies,
            'val_accuracies': val_accuracies,
            'best_val_acc': best_val_acc
        }

def main():
    print("VideoGPT+ Fine-tuning on Custom Dataset")
    print("=" * 50)
    
    # Dataset configuration
    dataset_configs = {
        'video_normale': r'C:\Users\<USER>\Desktop\video pour mohamed\video normale',
        'video_vol': r'C:\Users\<USER>\Desktop\video pour mohamed\video vol'
    }
    
    # Initialize fine-tuner
    fine_tuner = VideoGPTFineTuner(max_frames=16, target_size=(224, 224))
    
    # Prepare dataset
    video_paths, labels = fine_tuner.prepare_dataset(dataset_configs)
    
    if len(video_paths) == 0:
        print("No videos found! Please check your dataset paths.")
        return
    
    # Create data loaders
    train_loader, val_loader = fine_tuner.create_data_loaders(
        video_paths, labels, test_size=0.2, batch_size=2  # Small batch size for memory
    )
    
    # Train model
    model, history = fine_tuner.train_model(
        train_loader, val_loader, 
        num_epochs=15,  # Reduced epochs for faster training
        learning_rate=0.001
    )
    
    # Save training history
    with open('training_history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    print("\nFine-tuning completed successfully!")
    print(f"Final validation accuracy: {history['best_val_acc']:.2f}%")
    print("Model saved as: videogpt_finetuned_model.pth")
    print("Training history saved as: training_history.json")

if __name__ == "__main__":
    main()
