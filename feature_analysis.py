import torch
import torch.nn as nn
import torch.nn.functional as F
import cv2
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import random
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import json

# Import the model architecture
class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """
    
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x, return_features=False):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)
        
        # Apply temporal attention (simplified)
        features_expanded = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, attention_weights = self.temporal_attention(features_expanded, features_expanded, features_expanded)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        if return_features:
            return attended_features, attention_weights
        
        # Classification
        output = self.classifier(attended_features)
        
        return output

class FeatureAnalyzer:
    def __init__(self, model_path='videogpt_balanced_model.pth', device=None):
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = VideoClassifier(num_classes=2).to(self.device)
        
        # Load trained model
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            print(f"Loaded model from {model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")
            return
        
        self.model.eval()
        
    def load_video_frames(self, video_path, max_frames=16, target_size=(224, 224)):
        """Load and preprocess video frames"""
        cap = cv2.VideoCapture(str(video_path))
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames == 0:
            cap.release()
            return None
        
        # Calculate frame indices to sample uniformly
        if total_frames <= max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)
        
        cap.release()
        
        # Pad with zeros if not enough frames
        while len(frames) < max_frames:
            frames.append(torch.zeros(3, *target_size))
        
        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames[:max_frames])
        
        return frames_tensor
    
    def extract_features(self, video_paths, labels):
        """Extract features from videos"""
        features_list = []
        labels_list = []
        attention_weights_list = []
        
        print("Extracting features from videos...")
        for video_path, label in tqdm(zip(video_paths, labels)):
            frames = self.load_video_frames(video_path)
            if frames is not None:
                frames = frames.unsqueeze(0).to(self.device)  # Add batch dimension
                
                with torch.no_grad():
                    features, attention_weights = self.model(frames, return_features=True)
                    features_list.append(features.cpu().numpy())
                    labels_list.append(label)
                    attention_weights_list.append(attention_weights.cpu().numpy())
        
        return np.vstack(features_list), np.array(labels_list), attention_weights_list
    
    def analyze_feature_distribution(self, features, labels, save_path='feature_analysis'):
        """Analyze feature distribution using dimensionality reduction"""
        print("Analyzing feature distribution...")
        
        # Create save directory
        os.makedirs(save_path, exist_ok=True)
        
        # PCA Analysis
        print("Performing PCA...")
        pca = PCA(n_components=2)
        features_pca = pca.fit_transform(features)
        
        plt.figure(figsize=(12, 5))
        
        # PCA plot
        plt.subplot(1, 2, 1)
        colors = ['blue', 'red']
        class_names = ['Normal', 'Suspicious']
        
        for i, (color, class_name) in enumerate(zip(colors, class_names)):
            mask = labels == i
            plt.scatter(features_pca[mask, 0], features_pca[mask, 1], 
                       c=color, label=class_name, alpha=0.7)
        
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('PCA of Video Features')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # t-SNE Analysis
        print("Performing t-SNE...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(features)-1))
        features_tsne = tsne.fit_transform(features)
        
        plt.subplot(1, 2, 2)
        for i, (color, class_name) in enumerate(zip(colors, class_names)):
            mask = labels == i
            plt.scatter(features_tsne[mask, 0], features_tsne[mask, 1], 
                       c=color, label=class_name, alpha=0.7)
        
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        plt.title('t-SNE of Video Features')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{save_path}/feature_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # Feature importance analysis
        feature_means = np.mean(features, axis=0)
        feature_stds = np.std(features, axis=0)
        
        normal_features = features[labels == 0]
        suspicious_features = features[labels == 1]
        
        normal_means = np.mean(normal_features, axis=0)
        suspicious_means = np.mean(suspicious_features, axis=0)
        
        # Calculate feature differences
        feature_diff = np.abs(normal_means - suspicious_means)
        top_discriminative_features = np.argsort(feature_diff)[-20:]  # Top 20 discriminative features
        
        plt.figure(figsize=(12, 8))
        plt.subplot(2, 2, 1)
        plt.hist(feature_means, bins=50, alpha=0.7)
        plt.xlabel('Feature Mean Values')
        plt.ylabel('Frequency')
        plt.title('Distribution of Feature Means')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 2)
        plt.hist(feature_stds, bins=50, alpha=0.7)
        plt.xlabel('Feature Standard Deviations')
        plt.ylabel('Frequency')
        plt.title('Distribution of Feature Std Devs')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 3)
        plt.plot(normal_means, label='Normal', alpha=0.7)
        plt.plot(suspicious_means, label='Suspicious', alpha=0.7)
        plt.xlabel('Feature Index')
        plt.ylabel('Mean Value')
        plt.title('Feature Means by Class')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 4)
        plt.bar(range(len(top_discriminative_features)), feature_diff[top_discriminative_features])
        plt.xlabel('Top Discriminative Features')
        plt.ylabel('Absolute Difference')
        plt.title('Most Discriminative Features')
        plt.xticks(range(len(top_discriminative_features)), top_discriminative_features, rotation=45)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{save_path}/feature_statistics.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # Save analysis results
        analysis_results = {
            'pca_explained_variance': pca.explained_variance_ratio_.tolist(),
            'top_discriminative_features': top_discriminative_features.tolist(),
            'feature_differences': feature_diff[top_discriminative_features].tolist(),
            'normal_feature_means': normal_means.tolist(),
            'suspicious_feature_means': suspicious_means.tolist(),
            'total_explained_variance_pca': float(np.sum(pca.explained_variance_ratio_))
        }
        
        with open(f'{save_path}/analysis_results.json', 'w') as f:
            json.dump(analysis_results, f, indent=2)
        
        return analysis_results
    
    def analyze_attention_patterns(self, attention_weights, labels, save_path='feature_analysis'):
        """Analyze attention patterns"""
        print("Analyzing attention patterns...")

        normal_attention = [att for att, label in zip(attention_weights, labels) if label == 0]
        suspicious_attention = [att for att, label in zip(attention_weights, labels) if label == 1]

        # Check attention weights shape and handle accordingly
        if len(normal_attention) > 0 and len(suspicious_attention) > 0:
            # Get first attention weight to check shape
            sample_att = normal_attention[0]
            print(f"Attention weights shape: {sample_att.shape}")

            # Handle different attention weight shapes
            squeezed_sample = sample_att.squeeze()
            if squeezed_sample.ndim >= 1 and squeezed_sample.size > 1:
                # Average attention weights
                normal_avg_attention = np.mean([att.squeeze() for att in normal_attention], axis=0)
                suspicious_avg_attention = np.mean([att.squeeze() for att in suspicious_attention], axis=0)

                # Ensure 2D for visualization
                if normal_avg_attention.ndim == 1:
                    normal_avg_attention = normal_avg_attention.reshape(1, -1)
                if suspicious_avg_attention.ndim == 1:
                    suspicious_avg_attention = suspicious_avg_attention.reshape(1, -1)

                plt.figure(figsize=(12, 6))

                plt.subplot(1, 2, 1)
                plt.imshow(normal_avg_attention, cmap='viridis', aspect='auto')
                plt.title('Average Attention - Normal Videos')
                plt.xlabel('Key Position')
                plt.ylabel('Query Position')
                plt.colorbar()

                plt.subplot(1, 2, 2)
                plt.imshow(suspicious_avg_attention, cmap='viridis', aspect='auto')
                plt.title('Average Attention - Suspicious Videos')
                plt.xlabel('Key Position')
                plt.ylabel('Query Position')
                plt.colorbar()

                plt.tight_layout()
                plt.savefig(f'{save_path}/attention_patterns.png', dpi=300, bbox_inches='tight')
                plt.close()

                return {
                    'normal_avg_attention': normal_avg_attention.tolist(),
                    'suspicious_avg_attention': suspicious_avg_attention.tolist()
                }
            else:
                print("Attention weights are scalar, creating simple comparison plot")
                normal_avg = np.mean([att.squeeze().item() for att in normal_attention])
                suspicious_avg = np.mean([att.squeeze().item() for att in suspicious_attention])

                plt.figure(figsize=(8, 6))
                categories = ['Normal Videos', 'Suspicious Videos']
                values = [normal_avg, suspicious_avg]
                colors = ['blue', 'red']

                plt.bar(categories, values, color=colors, alpha=0.7)
                plt.ylabel('Average Attention Weight')
                plt.title('Average Attention Weights by Video Type')
                plt.grid(True, alpha=0.3)

                for i, v in enumerate(values):
                    plt.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom')

                plt.tight_layout()
                plt.savefig(f'{save_path}/attention_comparison.png', dpi=300, bbox_inches='tight')
                plt.close()

                return {
                    'normal_avg_attention': float(normal_avg),
                    'suspicious_avg_attention': float(suspicious_avg)
                }
        else:
            print("No attention weights to analyze")
            return {}

def run_feature_analysis():
    """Run complete feature analysis"""
    print("Starting feature analysis...")
    
    # Initialize analyzer
    analyzer = FeatureAnalyzer()
    
    # Collect video paths and labels
    normal_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video normale")
    suspicious_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video vol")
    
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    
    video_paths = []
    labels = []
    
    # Normal videos
    for ext in video_extensions:
        normal_videos = list(normal_dir.glob(f"*{ext}"))
        video_paths.extend(normal_videos)
        labels.extend([0] * len(normal_videos))
    
    # Suspicious videos
    for ext in video_extensions:
        suspicious_videos = list(suspicious_dir.glob(f"*{ext}"))
        video_paths.extend(suspicious_videos)
        labels.extend([1] * len(suspicious_videos))
    
    print(f"Found {len([l for l in labels if l == 0])} normal videos")
    print(f"Found {len([l for l in labels if l == 1])} suspicious videos")
    
    # Extract features
    features, labels_array, attention_weights = analyzer.extract_features(video_paths, labels)
    
    print(f"Extracted features shape: {features.shape}")
    
    # Analyze features
    feature_results = analyzer.analyze_feature_distribution(features, labels_array)
    attention_results = analyzer.analyze_attention_patterns(attention_weights, labels_array)
    
    # Combine results
    complete_results = {
        'feature_analysis': feature_results,
        'attention_analysis': attention_results,
        'dataset_info': {
            'total_videos': len(video_paths),
            'normal_videos': len([l for l in labels if l == 0]),
            'suspicious_videos': len([l for l in labels if l == 1]),
            'feature_dimension': features.shape[1]
        }
    }
    
    # Save complete results
    with open('feature_analysis/complete_analysis.json', 'w') as f:
        json.dump(complete_results, f, indent=2)
    
    print("Feature analysis completed!")
    print("Results saved in 'feature_analysis/' directory")
    
    return complete_results

if __name__ == "__main__":
    results = run_feature_analysis()
