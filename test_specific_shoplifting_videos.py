import torch
import torch.nn as nn
import cv2
import numpy as np
from pathlib import Path
import time

# Import the model architecture
class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """
    
    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()
        
        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),
            
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.<PERSON><PERSON>ool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),
            
            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]
        
        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)
        
        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.squeeze(-1).squeeze(-1).squeeze(-1)  # (batch_size, feature_dim)
        
        # Apply temporal attention (simplified)
        features_expanded = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features_expanded, features_expanded, features_expanded)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)
        
        # Classification
        output = self.classifier(attended_features)
        
        return output

def load_video_frames(video_path, max_frames=16, target_size=(224, 224)):
    """Load and preprocess video frames"""
    cap = cv2.VideoCapture(str(video_path))
    frames = []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames == 0:
        cap.release()
        return None
    
    # Calculate frame indices to sample uniformly
    if total_frames <= max_frames:
        frame_indices = list(range(total_frames))
    else:
        frame_indices = np.linspace(0, total_frames - 1, max_frames, dtype=int)
    
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if ret:
            # Convert BGR to RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # Resize frame
            frame = cv2.resize(frame, target_size)
            # Convert to tensor and normalize
            frame = torch.from_numpy(frame).float() / 255.0
            # Change from HWC to CHW
            frame = frame.permute(2, 0, 1)
            frames.append(frame)
    
    cap.release()
    
    # Pad with zeros if not enough frames
    while len(frames) < max_frames:
        frames.append(torch.zeros(3, *target_size))
    
    # Stack frames into tensor (T, C, H, W)
    frames_tensor = torch.stack(frames[:max_frames])
    
    return frames_tensor

def predict_video(model, video_path, device):
    """Predict class for a single video"""
    frames = load_video_frames(video_path)
    if frames is None:
        return None, None, "Failed to load video", None, None
    
    # Add batch dimension and move to device
    frames = frames.unsqueeze(0).to(device)
    
    with torch.no_grad():
        outputs = model(frames)
        probabilities = torch.softmax(outputs, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0][predicted_class].item()
        
        # Also show probabilities for both classes
        normal_prob = probabilities[0][0].item()
        suspicious_prob = probabilities[0][1].item()
    
    return predicted_class, confidence, None, normal_prob, suspicious_prob

def main():
    """Test the enhanced model on specific main shoplifting videos"""
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ Using device: {device}")
    
    model = VideoClassifier(num_classes=2).to(device)
    
    # Load trained model
    try:
        checkpoint = torch.load('videogpt_balanced_model.pth', map_location=device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print("✅ Loaded enhanced model from videogpt_balanced_model.pth")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    model.eval()
    class_names = ['Normal', 'Suspicious']
    
    # Test specific main shoplifting videos
    test_videos = [
        "shoplifting/Shoplifting001_x264.mp4",
        "shoplifting/Shoplifting005_x264.mp4", 
        "shoplifting/Shoplifting006_x264.mp4",
        "shoplifting/Shoplifting007_x264.mp4",
        "shoplifting/Shoplifting009_x264.mp4",
        "shoplifting/Shoplifting010_x264.mp4",
        "shoplifting/Shoplifting013_x264.mp4",
        "shoplifting/Shoplifting015_x264.mp4",
        "shoplifting/Shoplifting016_x264.mp4",
        "shoplifting/Shoplifting018_x264.mp4"
    ]
    
    print(f"🎯 Testing {len(test_videos)} main shoplifting videos:")
    print("=" * 80)
    
    correct_predictions = 0
    total_predictions = 0
    processing_times = []
    confidence_scores = []
    
    for i, video_path in enumerate(test_videos, 1):
        print(f"\n📹 Test {i}/{len(test_videos)}: {Path(video_path).name}")
        print(f"📂 Path: {video_path}")
        print(f"📋 Expected: Suspicious (shoplifting video)")
        
        start_time = time.time()
        result = predict_video(model, video_path, device)
        processing_time = time.time() - start_time
        processing_times.append(processing_time)
        
        if len(result) == 5:
            predicted_class, confidence, error, normal_prob, suspicious_prob = result
            
            if error:
                print(f"❌ Error: {error}")
                continue
            
            total_predictions += 1
            expected_class_id = 1  # Should be suspicious
            is_correct = predicted_class == expected_class_id
            confidence_scores.append(confidence)
            
            if is_correct:
                correct_predictions += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            predicted_label = class_names[predicted_class]
            
            print(f"🎯 Predicted: {predicted_label}")
            print(f"📊 Confidence: {confidence:.1%}")
            print(f"⏱️ Processing Time: {processing_time:.2f}s")
            print(f"🏆 {status}")
            
            # Show detailed probabilities
            print(f"📊 Detailed Probabilities:")
            print(f"   Normal: {normal_prob:.1%}")
            print(f"   Suspicious: {suspicious_prob:.1%}")
            
            if not is_correct:
                print(f"⚠️ Analysis: This main video was misclassified")
        else:
            print("❌ Unexpected result format")
    
    # Summary statistics
    print("\n" + "=" * 80)
    print("📊 MAIN SHOPLIFTING VIDEOS TEST RESULTS:")
    print("=" * 80)
    
    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        
        print(f"🎯 Overall Accuracy: {correct_predictions}/{total_predictions} ({accuracy:.1%})")
        print(f"📊 Average Confidence: {avg_confidence:.1%}")
        print(f"⏱️ Average Processing Time: {avg_processing_time:.2f}s")
        print(f"🏆 Performance Rating: {'EXCELLENT' if accuracy >= 0.9 else 'GOOD' if accuracy >= 0.8 else 'FAIR' if accuracy >= 0.7 else 'NEEDS IMPROVEMENT'}")
        
        print(f"\n📈 Detailed Statistics:")
        print(f"   Correct Detections: {correct_predictions}")
        print(f"   Missed Detections: {total_predictions - correct_predictions}")
        print(f"   Total Main Videos Tested: {total_predictions}")
        
        # Confidence distribution
        if confidence_scores:
            high_confidence = sum(1 for c in confidence_scores if c >= 0.8)
            medium_confidence = sum(1 for c in confidence_scores if 0.6 <= c < 0.8)
            low_confidence = sum(1 for c in confidence_scores if c < 0.6)
            
            print(f"\n🎯 Confidence Distribution:")
            print(f"   High Confidence (≥80%): {high_confidence}/{total_predictions} ({high_confidence/total_predictions:.1%})")
            print(f"   Medium Confidence (60-79%): {medium_confidence}/{total_predictions} ({medium_confidence/total_predictions:.1%})")
            print(f"   Low Confidence (<60%): {low_confidence}/{total_predictions} ({low_confidence/total_predictions:.1%})")
        
        print(f"\n🔍 Analysis:")
        print("📝 Key Observations:")
        print("   • Model was trained on general 'suspicious' vs 'normal' behavior from user's dataset")
        print("   • Shoplifting videos represent a specific domain of suspicious behavior")
        print("   • Performance on main videos vs segments shows domain adaptation challenges")
        print("   • Model shows high confidence even when wrong, indicating domain gap")
        
        if accuracy >= 0.7:
            print("✅ The model shows reasonable cross-domain performance")
            print("🎯 Main videos perform better than short segments")
        else:
            print("⚠️ The model needs domain-specific fine-tuning for shoplifting detection")
            print("🔧 Consider training with shoplifting examples or domain adaptation techniques")
        
        print(f"\n💡 Recommendations:")
        print("   • Current model works well for general suspicious behavior detection")
        print("   • For shoplifting-specific detection, consider fine-tuning with shoplifting data")
        print("   • The model architecture is sound - domain adaptation would help")
        print("   • Consider ensemble methods combining general and domain-specific models")
    
    return {
        'accuracy': accuracy if total_predictions > 0 else 0,
        'correct': correct_predictions,
        'total': total_predictions,
        'avg_confidence': avg_confidence if total_predictions > 0 else 0,
        'avg_processing_time': avg_processing_time if total_predictions > 0 else 0,
        'confidence_scores': confidence_scores,
        'processing_times': processing_times
    }

if __name__ == "__main__":
    results = main()
