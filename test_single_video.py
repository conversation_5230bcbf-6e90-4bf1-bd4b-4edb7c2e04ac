import torch
import torch.nn as nn
import cv2
import numpy as np
import time
from pathlib import Path

class VideoClassifier(nn.Module):
    """
    Simple video classifier for fine-tuning
    """

    def __init__(self, num_classes=2, max_frames=16, feature_dim=512):
        super(VideoClassifier, self).__init__()

        # CNN backbone for spatial features
        self.spatial_encoder = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1)),

            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),

            nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2)),

            nn.Conv3d(256, feature_dim, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1)),
            nn.BatchNorm3d(feature_dim),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )

        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)

        # Classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )

    def forward(self, x):
        # x shape: (batch_size, time, channels, height, width)
        batch_size, time_steps = x.shape[:2]

        # Reshape for 3D CNN: (batch_size, channels, time, height, width)
        x = x.permute(0, 2, 1, 3, 4)

        # Extract spatial-temporal features
        features = self.spatial_encoder(x)  # (batch_size, feature_dim, 1, 1, 1)
        features = features.view(batch_size, -1)  # (batch_size, feature_dim)

        # Apply temporal attention
        features_att = features.unsqueeze(1)  # (batch_size, 1, feature_dim)
        attended_features, _ = self.temporal_attention(features_att, features_att, features_att)
        attended_features = attended_features.squeeze(1)  # (batch_size, feature_dim)

        # Classification
        output = self.classifier(attended_features)
        return output

def load_and_preprocess_video(video_path, target_frames=16, target_size=(224, 224)):
    """Load and preprocess a single video file"""
    if not Path(video_path).exists():
        raise FileNotFoundError(f"Video file not found: {video_path}")
    
    cap = cv2.VideoCapture(video_path)
    frames = []
    
    # Get total frame count
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    if total_frames == 0:
        raise ValueError(f"Could not read video: {video_path}")
    
    # Calculate frame indices for uniform sampling
    if total_frames >= target_frames:
        frame_indices = np.linspace(0, total_frames - 1, target_frames, dtype=int)
    else:
        # If video has fewer frames, repeat frames
        frame_indices = np.arange(total_frames)
        while len(frame_indices) < target_frames:
            frame_indices = np.concatenate([frame_indices, frame_indices])
        frame_indices = frame_indices[:target_frames]
    
    # Extract frames
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            # Resize frame
            frame = cv2.resize(frame, target_size)
            # Convert BGR to RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame)
    
    cap.release()
    
    if len(frames) != target_frames:
        raise ValueError(f"Expected {target_frames} frames, got {len(frames)}")
    
    # Convert to tensor and normalize
    frames = np.array(frames, dtype=np.float32) / 255.0

    # Expected format: (batch_size, time, channels, height, width)
    # Current: (frames, height, width, channels)
    # Rearrange to: (frames, channels, height, width) then add batch dim
    frames = np.transpose(frames, (0, 3, 1, 2))  # (frames, channels, height, width)

    # Add batch dimension: (1, frames, channels, height, width)
    frames = np.expand_dims(frames, axis=0)

    return torch.FloatTensor(frames)

def test_video(video_path, model_path="videogpt_finetuned_model.pth"):
    """Test a single video with the fine-tuned model"""
    
    print(f"Testing video: {video_path}")
    print("=" * 60)
    
    # Check if model file exists
    if not Path(model_path).exists():
        raise FileNotFoundError(f"Model file not found: {model_path}")
    
    # Load model
    print("Loading fine-tuned model...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    model = VideoClassifier(num_classes=2)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    print(f"Model loaded successfully! Best validation accuracy: {checkpoint.get('best_val_acc', 'N/A'):.2%}")
    
    # Load and preprocess video
    print("\nPreprocessing video...")
    start_time = time.time()
    
    try:
        video_tensor = load_and_preprocess_video(video_path)
        video_tensor = video_tensor.to(device)
        
        preprocessing_time = time.time() - start_time
        print(f"Video preprocessed in {preprocessing_time:.3f} seconds")
        print(f"Video tensor shape: {video_tensor.shape}")
        
        # Make prediction
        print("\nMaking prediction...")
        inference_start = time.time()
        
        with torch.no_grad():
            outputs = model(video_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = torch.max(probabilities, dim=1)[0].item()
        
        inference_time = time.time() - inference_start
        total_time = time.time() - start_time
        
        # Class mapping
        class_names = {0: "Normal", 1: "Vol"}
        
        # Display results
        print("\n" + "=" * 60)
        print("🎯 PREDICTION RESULTS")
        print("=" * 60)
        print(f"📁 Video File: {Path(video_path).name}")
        print(f"🏷️  Predicted Class: {class_names[predicted_class]}")
        print(f"🎯 Confidence: {confidence:.4f} ({confidence*100:.2f}%)")
        print(f"📊 Class Probabilities:")
        print(f"   Normal: {probabilities[0][0].item():.4f} ({probabilities[0][0].item()*100:.2f}%)")
        print(f"   Vol:    {probabilities[0][1].item():.4f} ({probabilities[0][1].item()*100:.2f}%)")
        print(f"⏱️  Processing Times:")
        print(f"   Preprocessing: {preprocessing_time:.3f}s")
        print(f"   Inference: {inference_time:.3f}s")
        print(f"   Total: {total_time:.3f}s")
        print("=" * 60)
        
        return {
            'predicted_class': predicted_class,
            'class_name': class_names[predicted_class],
            'confidence': confidence,
            'probabilities': {
                'Normal': probabilities[0][0].item(),
                'Vol': probabilities[0][1].item()
            },
            'processing_time': total_time,
            'inference_time': inference_time
        }
        
    except Exception as e:
        print(f"❌ Error processing video: {str(e)}")
        return None

if __name__ == "__main__":
    # Test the specific video
    video_path = r"C:\Users\<USER>\Desktop\video pour mohamed\video vol\test.mp4"
    
    try:
        result = test_video(video_path)
        if result:
            print(f"\n✅ Test completed successfully!")
            print(f"Final prediction: {result['class_name']} (confidence: {result['confidence']:.2%})")
        else:
            print("\n❌ Test failed!")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
