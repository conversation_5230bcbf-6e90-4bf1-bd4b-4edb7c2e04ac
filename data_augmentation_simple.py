import torch
import cv2
import numpy as np
import os
from pathlib import Path
import random
from tqdm import tqdm

class SimpleVideoAugmentation:
    def __init__(self, target_size=(224, 224)):
        self.target_size = target_size
        
    def horizontal_flip(self, frame):
        """Apply horizontal flip to frame"""
        return cv2.flip(frame, 1)
    
    def adjust_brightness_contrast(self, frame, brightness=0, contrast=1.0):
        """Adjust brightness and contrast"""
        frame = frame.astype(np.float32)
        frame = frame * contrast + brightness
        frame = np.clip(frame, 0, 255)
        return frame.astype(np.uint8)
    
    def add_gaussian_noise(self, frame, noise_level=25):
        """Add Gaussian noise to frame"""
        noise = np.random.normal(0, noise_level, frame.shape).astype(np.float32)
        noisy_frame = frame.astype(np.float32) + noise
        return np.clip(noisy_frame, 0, 255).astype(np.uint8)
    
    def apply_blur(self, frame, kernel_size=3):
        """Apply Gaussian blur"""
        return cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
    
    def rotate_frame(self, frame, angle):
        """Rotate frame by given angle"""
        h, w = frame.shape[:2]
        center = (w // 2, h // 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(frame, rotation_matrix, (w, h))
    
    def augment_frame(self, frame):
        """Apply random spatial augmentations to a single frame"""
        # Horizontal flip (50% chance)
        if random.random() < 0.5:
            frame = self.horizontal_flip(frame)
        
        # Brightness and contrast adjustment (50% chance)
        if random.random() < 0.5:
            brightness = random.uniform(-30, 30)
            contrast = random.uniform(0.8, 1.2)
            frame = self.adjust_brightness_contrast(frame, brightness, contrast)
        
        # Gaussian noise (30% chance)
        if random.random() < 0.3:
            noise_level = random.uniform(10, 30)
            frame = self.add_gaussian_noise(frame, noise_level)
        
        # Blur (20% chance)
        if random.random() < 0.2:
            kernel_size = random.choice([3, 5])
            frame = self.apply_blur(frame, kernel_size)
        
        # Rotation (30% chance)
        if random.random() < 0.3:
            angle = random.uniform(-15, 15)
            frame = self.rotate_frame(frame, angle)
        
        return frame
    
    def speed_change(self, frames, speed_factor):
        """Change video speed by sampling frames"""
        if speed_factor > 1.0:  # Speed up - skip frames
            indices = np.linspace(0, len(frames)-1, int(len(frames)/speed_factor), dtype=int)
            return [frames[i] for i in indices]
        elif speed_factor < 1.0:  # Slow down - duplicate frames
            new_frames = []
            for frame in frames:
                repeat_count = int(1/speed_factor)
                new_frames.extend([frame] * repeat_count)
            return new_frames[:len(frames)]  # Keep original length
        return frames
    
    def frame_dropout(self, frames, dropout_rate=0.1):
        """Randomly drop frames and interpolate"""
        if len(frames) < 3:
            return frames
        
        new_frames = frames.copy()
        num_drops = int(len(frames) * dropout_rate)
        
        for _ in range(num_drops):
            # Don't drop first or last frame
            drop_idx = random.randint(1, len(frames)-2)
            # Simple interpolation between adjacent frames
            prev_frame = new_frames[drop_idx-1].astype(np.float32)
            next_frame = new_frames[drop_idx+1].astype(np.float32)
            interpolated = ((prev_frame + next_frame) / 2).astype(np.uint8)
            new_frames[drop_idx] = interpolated
        
        return new_frames
    
    def temporal_shift(self, frames, shift_frames=2):
        """Shift frames temporally"""
        if shift_frames >= len(frames):
            return frames
        
        # Circular shift
        return frames[shift_frames:] + frames[:shift_frames]
    
    def reverse_playback(self, frames):
        """Reverse the order of frames"""
        return frames[::-1]
    
    def apply_temporal_augmentation(self, frames):
        """Apply random temporal augmentation"""
        augmentation = random.choice([
            'speed_change', 'frame_dropout', 'temporal_shift', 'reverse_playback', 'none'
        ])
        
        if augmentation == 'speed_change':
            speed_factor = random.choice([0.8, 1.2, 1.5])
            return self.speed_change(frames, speed_factor)
        elif augmentation == 'frame_dropout':
            dropout_rate = random.uniform(0.05, 0.15)
            return self.frame_dropout(frames, dropout_rate)
        elif augmentation == 'temporal_shift':
            shift = random.randint(1, min(3, len(frames)//4))
            return self.temporal_shift(frames, shift)
        elif augmentation == 'reverse_playback':
            return self.reverse_playback(frames)
        else:
            return frames
    
    def load_video_frames(self, video_path, num_frames=16):
        """Load frames from video file"""
        cap = cv2.VideoCapture(str(video_path))
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames == 0:
            cap.release()
            return None
        
        # Sample frames uniformly
        frame_indices = np.linspace(0, total_frames-1, num_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                frames.append(frame)
        
        cap.release()
        return frames if len(frames) == num_frames else None
    
    def augment_video(self, video_path, num_frames=16):
        """Apply augmentations to entire video"""
        frames = self.load_video_frames(video_path, num_frames)
        if frames is None:
            return None
        
        # Apply spatial augmentations to each frame
        augmented_frames = []
        for frame in frames:
            aug_frame = self.augment_frame(frame)
            augmented_frames.append(aug_frame)
        
        # Apply temporal augmentation
        augmented_frames = self.apply_temporal_augmentation(augmented_frames)
        
        # Ensure we have exactly num_frames
        if len(augmented_frames) != num_frames:
            # Resample to get exact number of frames
            indices = np.linspace(0, len(augmented_frames)-1, num_frames, dtype=int)
            augmented_frames = [augmented_frames[i] for i in indices]
        
        return augmented_frames

def create_augmented_dataset():
    """Create augmented dataset for normal videos"""
    print("Creating augmented dataset for normal videos...")
    
    # Paths
    normal_videos_dir = Path("C:/Users/<USER>/Desktop/video pour mohamed/video normale")
    
    # Check if directory exists
    if not normal_videos_dir.exists():
        print(f"Error: Directory {normal_videos_dir} not found!")
        return
    
    # Get list of normal videos
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    normal_videos = []
    for ext in video_extensions:
        normal_videos.extend(list(normal_videos_dir.glob(f"*{ext}")))
    
    print(f"Found {len(normal_videos)} normal videos")
    
    if len(normal_videos) == 0:
        print("No video files found!")
        return
    
    # Initialize augmentation
    augmenter = SimpleVideoAugmentation()
    
    # Target: Generate enough augmented videos to balance with suspicious videos (38)
    target_normal_count = 38
    current_normal_count = len(normal_videos)
    augmentations_needed = max(0, target_normal_count - current_normal_count)
    
    print(f"Current normal videos: {current_normal_count}")
    print(f"Target normal videos: {target_normal_count}")
    print(f"Augmentations needed: {augmentations_needed}")
    
    if augmentations_needed == 0:
        print("Dataset is already balanced!")
        return
    
    # Create augmented videos
    augmented_videos = []
    augmented_labels = []
    
    # Process original normal videos first
    print("Processing original normal videos...")
    for video_path in tqdm(normal_videos):
        frames = augmenter.load_video_frames(video_path)
        if frames is not None:
            # Convert frames to tensor format
            video_tensor = torch.stack([
                torch.from_numpy(frame.transpose(2, 0, 1)).float() / 255.0 
                for frame in frames
            ])
            augmented_videos.append(video_tensor)
            augmented_labels.append(0)  # Normal class
    
    # Generate additional augmented videos
    print(f"Generating {augmentations_needed} augmented videos...")
    for i in tqdm(range(augmentations_needed)):
        # Randomly select a normal video to augment
        source_video = random.choice(normal_videos)
        
        # Apply augmentations
        augmented_frames = augmenter.augment_video(source_video)
        if augmented_frames is not None:
            # Convert to tensor format
            video_tensor = torch.stack([
                torch.from_numpy(frame.transpose(2, 0, 1)).float() / 255.0 
                for frame in augmented_frames
            ])
            augmented_videos.append(video_tensor)
            augmented_labels.append(0)  # Normal class
    
    # Save augmented dataset
    print("Saving augmented dataset...")
    augmented_dataset = {
        'videos': torch.stack(augmented_videos),
        'labels': torch.tensor(augmented_labels)
    }
    
    torch.save(augmented_dataset, 'augmented_normal_videos.pth')
    print(f"Saved {len(augmented_videos)} augmented normal videos to 'augmented_normal_videos.pth'")
    print(f"Dataset shape: {augmented_dataset['videos'].shape}")

if __name__ == "__main__":
    create_augmented_dataset()
