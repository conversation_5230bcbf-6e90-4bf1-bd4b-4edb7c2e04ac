#!/usr/bin/env python3
"""
Evaluate Fine-tuned VideoGPT+ Model
Tests the fine-tuned model and provides F1 score, accuracy, and processing time metrics
"""

import os
import sys
import torch
import torch.nn as nn
import cv2
import numpy as np
import time
import json
import glob
from pathlib import Path
from tqdm import tqdm
from sklearn.metrics import f1_score, accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Add VideoGPT-plus to path
videogpt_path = os.path.abspath('VideoGPT-plus')
if videogpt_path not in sys.path:
    sys.path.insert(0, videogpt_path)

# Import the model architecture from fine-tuning script
from finetune_videogpt import VideoClassifier

class ModelEvaluator:
    """
    Evaluator for fine-tuned VideoGPT+ model
    """
    
    def __init__(self, model_path='videogpt_finetuned_model.pth', max_frames=16, target_size=(224, 224)):
        self.max_frames = max_frames
        self.target_size = target_size
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        
        # Load the fine-tuned model
        self.model = self.load_model(model_path)
        
        print(f"Model evaluator initialized on device: {self.device}")
        
    def load_model(self, model_path):
        """Load the fine-tuned model"""
        print(f"Loading fine-tuned model from: {model_path}")
        
        # Initialize model architecture
        model = VideoClassifier(num_classes=2, max_frames=self.max_frames).to(self.device)
        
        if os.path.exists(model_path):
            # Load trained weights
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"Model loaded successfully! Best validation accuracy: {checkpoint.get('best_val_acc', 'N/A'):.2f}%")
        else:
            print(f"Warning: Model file {model_path} not found. Using randomly initialized model.")
        
        model.eval()
        return model
    
    def load_video_frames(self, video_path):
        """Load and preprocess video frames with timing"""
        start_time = time.time()
        
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Calculate frame indices to sample uniformly
        if total_frames <= self.max_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, self.max_frames, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                # Resize frame
                frame = cv2.resize(frame, self.target_size)
                # Convert to tensor and normalize
                frame = torch.from_numpy(frame).float() / 255.0
                # Change from HWC to CHW
                frame = frame.permute(2, 0, 1)
                frames.append(frame)
        
        cap.release()
        
        # Pad with zeros if not enough frames
        while len(frames) < self.max_frames:
            frames.append(torch.zeros(3, *self.target_size))
        
        # Stack frames into tensor (T, C, H, W)
        frames_tensor = torch.stack(frames[:self.max_frames]).unsqueeze(0).to(self.device)  # Add batch dimension
        
        load_time = time.time() - start_time
        
        return frames_tensor, load_time, {'total_frames': total_frames, 'fps': fps}
    
    def predict_video(self, video_frames):
        """Predict video class using fine-tuned model"""
        start_time = time.time()
        
        with torch.no_grad():
            outputs = self.model(video_frames)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_class = torch.argmax(outputs, dim=1).item()
            confidence = torch.max(probabilities, dim=1)[0].item()
        
        inference_time = time.time() - start_time
        
        return predicted_class, confidence, inference_time
    
    def scan_dataset(self, dataset_path):
        """Scan dataset for video files"""
        videos = []
        if os.path.exists(dataset_path):
            for ext in self.video_extensions:
                pattern = os.path.join(dataset_path, f"**/*{ext}")
                videos.extend(glob.glob(pattern, recursive=True))
        return videos
    
    def evaluate_datasets(self, dataset_configs):
        """Evaluate model on all datasets"""
        print("Starting Fine-tuned Model Evaluation")
        print("=" * 50)
        
        all_predictions = []
        all_true_labels = []
        all_confidences = []
        all_processing_times = []
        detailed_results = []
        
        label_mapping = {
            'video_normale': 0,  # Normal videos
            'video_vol': 1       # Vol videos
        }
        
        class_names = ['Normal Videos', 'Vol Videos']
        
        total_videos = 0
        successful_videos = 0
        failed_videos = 0
        
        for dataset_name, dataset_path in dataset_configs.items():
            print(f"\nEvaluating dataset: {dataset_name}")
            print(f"Path: {dataset_path}")
            
            videos = self.scan_dataset(dataset_path)
            true_label = label_mapping[dataset_name]
            
            print(f"Found {len(videos)} videos")
            total_videos += len(videos)
            
            dataset_start_time = time.time()
            
            for video_path in tqdm(videos, desc=f"Processing {dataset_name}"):
                try:
                    # Load video frames
                    frames, load_time, metadata = self.load_video_frames(video_path)
                    
                    # Predict using fine-tuned model
                    predicted_class, confidence, inference_time = self.predict_video(frames)
                    
                    total_time = load_time + inference_time
                    
                    # Store results
                    all_predictions.append(predicted_class)
                    all_true_labels.append(true_label)
                    all_confidences.append(confidence)
                    all_processing_times.append(total_time)
                    
                    detailed_results.append({
                        'video_path': video_path,
                        'filename': os.path.basename(video_path),
                        'dataset': dataset_name,
                        'true_label': true_label,
                        'true_class': class_names[true_label],
                        'predicted_label': predicted_class,
                        'predicted_class': class_names[predicted_class],
                        'confidence': confidence,
                        'correct': predicted_class == true_label,
                        'load_time': load_time,
                        'inference_time': inference_time,
                        'total_processing_time': total_time,
                        'metadata': metadata
                    })
                    
                    successful_videos += 1
                    
                except Exception as e:
                    print(f"Error processing {os.path.basename(video_path)}: {e}")
                    failed_videos += 1
                    continue
            
            dataset_time = time.time() - dataset_start_time
            print(f"Dataset {dataset_name} processed in {dataset_time:.2f} seconds")
        
        print(f"\nProcessing Summary:")
        print(f"Total videos: {total_videos}")
        print(f"Successfully processed: {successful_videos}")
        print(f"Failed: {failed_videos}")
        
        return all_predictions, all_true_labels, all_confidences, all_processing_times, detailed_results
    
    def calculate_metrics(self, predictions, true_labels, confidences, processing_times):
        """Calculate comprehensive evaluation metrics"""
        # Classification metrics
        accuracy = accuracy_score(true_labels, predictions)
        f1_weighted = f1_score(true_labels, predictions, average='weighted')
        f1_macro = f1_score(true_labels, predictions, average='macro')
        f1_micro = f1_score(true_labels, predictions, average='micro')
        
        # Per-class F1 scores
        f1_per_class = f1_score(true_labels, predictions, average=None)
        
        # Processing time metrics
        avg_processing_time = np.mean(processing_times)
        total_processing_time = np.sum(processing_times)
        videos_per_second = len(processing_times) / total_processing_time if total_processing_time > 0 else 0
        
        # Confidence metrics
        avg_confidence = np.mean(confidences)
        
        metrics = {
            'accuracy': accuracy,
            'f1_weighted': f1_weighted,
            'f1_macro': f1_macro,
            'f1_micro': f1_micro,
            'f1_normal_videos': f1_per_class[0] if len(f1_per_class) > 0 else 0,
            'f1_vol_videos': f1_per_class[1] if len(f1_per_class) > 1 else 0,
            'avg_processing_time': avg_processing_time,
            'total_processing_time': total_processing_time,
            'videos_per_second': videos_per_second,
            'avg_confidence': avg_confidence,
            'total_videos': len(predictions)
        }
        
        return metrics
    
    def print_detailed_results(self, metrics, predictions, true_labels, detailed_results):
        """Print comprehensive evaluation results"""
        print("\n" + "=" * 70)
        print("FINE-TUNED VIDEOGPT+ MODEL EVALUATION RESULTS")
        print("=" * 70)
        
        print(f"\n🎯 CLASSIFICATION PERFORMANCE:")
        print(f"   Overall Accuracy:     {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
        print(f"   F1 Score (Weighted):  {metrics['f1_weighted']:.4f}")
        print(f"   F1 Score (Macro):     {metrics['f1_macro']:.4f}")
        print(f"   F1 Score (Micro):     {metrics['f1_micro']:.4f}")
        
        print(f"\n📊 PER-CLASS F1 SCORES:")
        print(f"   Normal Videos F1:     {metrics['f1_normal_videos']:.4f}")
        print(f"   Vol Videos F1:        {metrics['f1_vol_videos']:.4f}")
        
        print(f"\n⏱️  PROCESSING TIME PERFORMANCE:")
        print(f"   Total Videos:         {metrics['total_videos']}")
        print(f"   Avg Time per Video:   {metrics['avg_processing_time']:.4f} seconds")
        print(f"   Total Processing:     {metrics['total_processing_time']:.2f} seconds")
        print(f"   Videos per Second:    {metrics['videos_per_second']:.2f} fps")
        
        print(f"\n🎲 CONFIDENCE METRICS:")
        print(f"   Average Confidence:   {metrics['avg_confidence']:.4f}")
        
        # Detailed classification report
        class_names = ['Normal Videos', 'Vol Videos']
        print(f"\n📋 DETAILED CLASSIFICATION REPORT:")
        report = classification_report(true_labels, predictions, target_names=class_names)
        print(report)
        
        # Confusion matrix
        cm = confusion_matrix(true_labels, predictions)
        print(f"\n🔢 CONFUSION MATRIX:")
        print(f"                    Predicted")
        print(f"                Normal    Vol")
        print(f"Actual Normal    {cm[0,0]:4d}    {cm[0,1]:4d}")
        print(f"       Vol       {cm[1,0]:4d}    {cm[1,1]:4d}")
        
        # Per-dataset breakdown
        print(f"\n📈 PER-DATASET BREAKDOWN:")
        for dataset in ['video_normale', 'video_vol']:
            dataset_results = [r for r in detailed_results if r['dataset'] == dataset]
            if dataset_results:
                correct = sum(1 for r in dataset_results if r['correct'])
                total = len(dataset_results)
                accuracy = correct / total if total > 0 else 0
                avg_conf = np.mean([r['confidence'] for r in dataset_results])
                avg_time = np.mean([r['total_processing_time'] for r in dataset_results])
                
                print(f"   {dataset}:")
                print(f"     Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
                print(f"     Avg Confidence: {avg_conf:.4f}")
                print(f"     Avg Processing Time: {avg_time:.4f}s")

def main():
    # Dataset configuration
    dataset_configs = {
        'video_normale': r'C:\Users\<USER>\Desktop\video pour mohamed\video normale',
        'video_vol': r'C:\Users\<USER>\Desktop\video pour mohamed\video vol'
    }
    
    # Initialize evaluator
    evaluator = ModelEvaluator()
    
    # Run evaluation on all videos
    predictions, true_labels, confidences, processing_times, detailed_results = evaluator.evaluate_datasets(dataset_configs)
    
    if len(predictions) == 0:
        print("No videos were successfully processed!")
        return
    
    # Calculate metrics
    metrics = evaluator.calculate_metrics(predictions, true_labels, confidences, processing_times)
    
    # Print detailed results
    evaluator.print_detailed_results(metrics, predictions, true_labels, detailed_results)
    
    # Save results
    results_data = {
        'metrics': metrics,
        'detailed_results': detailed_results,
        'evaluation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'model_info': {
            'model_type': 'Fine-tuned VideoGPT+',
            'max_frames': evaluator.max_frames,
            'target_size': evaluator.target_size,
            'device': evaluator.device
        }
    }
    
    with open('finetuned_model_evaluation.json', 'w') as f:
        json.dump(results_data, f, indent=2, default=str)
    
    print(f"\n💾 Detailed evaluation results saved to: finetuned_model_evaluation.json")
    
    # Summary for user
    print(f"\n🎉 FINAL SUMMARY:")
    print(f"   ✅ Accuracy: {metrics['accuracy']*100:.2f}%")
    print(f"   ✅ F1 Score: {metrics['f1_weighted']:.4f}")
    print(f"   ✅ Processing Speed: {metrics['videos_per_second']:.2f} videos/second")
    print(f"   ✅ Average Time per Video: {metrics['avg_processing_time']:.4f} seconds")

if __name__ == "__main__":
    main()
