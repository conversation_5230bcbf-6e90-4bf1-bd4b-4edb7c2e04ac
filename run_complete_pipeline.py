#!/usr/bin/env python3
"""
Complete VideoGPT+ Fine-tuning and Evaluation Pipeline
Runs the full pipeline: fine-tuning on 70 videos + evaluation with metrics
"""

import os
import sys
import subprocess
import time
import json

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    print(f"Running: {command}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\nCommand completed in {duration:.2f} seconds")
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print("\nOutput:")
                print(result.stdout)
        else:
            print("❌ FAILED")
            if result.stderr:
                print("\nError:")
                print(result.stderr)
            if result.stdout:
                print("\nOutput:")
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False
    
    return True

def check_prerequisites():
    """Check if all required files exist"""
    print("Checking prerequisites...")
    
    required_files = [
        'finetune_videogpt.py',
        'evaluate_finetuned_model.py',
        'VideoGPT-plus'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All prerequisites found")
    return True

def check_datasets():
    """Check if datasets exist and count videos"""
    dataset_configs = {
        'video_normale': r'C:\Users\<USER>\Desktop\video pour mohamed\video normale',
        'video_vol': r'C:\Users\<USER>\Desktop\video pour mohamed\video vol'
    }
    
    print("\nChecking datasets...")
    
    total_videos = 0
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    
    for dataset_name, dataset_path in dataset_configs.items():
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset path not found: {dataset_path}")
            return False
        
        import glob
        videos = []
        for ext in video_extensions:
            pattern = os.path.join(dataset_path, f"**/*{ext}")
            videos.extend(glob.glob(pattern, recursive=True))
        
        print(f"   {dataset_name}: {len(videos)} videos")
        total_videos += len(videos)
    
    print(f"✅ Total videos found: {total_videos}")
    
    if total_videos < 10:
        print("⚠️  Warning: Very few videos found. Results may not be reliable.")
    
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\nInstalling dependencies...")
    
    packages = [
        "torch torchvision --index-url https://download.pytorch.org/whl/cu118",
        "scikit-learn",
        "matplotlib",
        "seaborn",
        "tqdm",
        "opencv-python"
    ]
    
    for package in packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️  Warning: Failed to install {package}")
    
    return True

def main():
    print("🚀 VideoGPT+ Complete Fine-tuning and Evaluation Pipeline")
    print("=" * 70)
    print("This pipeline will:")
    print("1. Check prerequisites and datasets")
    print("2. Install required dependencies")
    print("3. Fine-tune VideoGPT+ on your 70 videos")
    print("4. Evaluate the model with F1 score, accuracy, and processing time")
    print("=" * 70)
    
    # Step 1: Check prerequisites
    if not check_prerequisites():
        print("❌ Prerequisites check failed. Please ensure all files are present.")
        return
    
    # Step 2: Check datasets
    if not check_datasets():
        print("❌ Dataset check failed. Please verify your video paths.")
        return
    
    # Step 3: Install dependencies
    print("\nWould you like to install/update dependencies? (y/n): ", end="")
    install_deps = input().lower().strip() == 'y'
    
    if install_deps:
        install_dependencies()
    
    # Step 4: Fine-tune the model
    print(f"\n🎯 Starting fine-tuning process...")
    print("This will train the model on all your videos (may take 30-60 minutes)")
    
    finetune_success = run_command(
        "python finetune_videogpt.py",
        "Fine-tuning VideoGPT+ on custom dataset"
    )
    
    if not finetune_success:
        print("❌ Fine-tuning failed. Please check the error messages above.")
        return
    
    # Check if model was saved
    if not os.path.exists('videogpt_finetuned_model.pth'):
        print("❌ Fine-tuned model not found. Training may have failed.")
        return
    
    print("✅ Fine-tuning completed successfully!")
    
    # Step 5: Evaluate the model
    print(f"\n📊 Starting model evaluation...")
    print("This will test the model on all videos and calculate metrics")
    
    evaluation_success = run_command(
        "python evaluate_finetuned_model.py",
        "Evaluating fine-tuned model performance"
    )
    
    if not evaluation_success:
        print("❌ Evaluation failed. Please check the error messages above.")
        return
    
    print("✅ Evaluation completed successfully!")
    
    # Step 6: Display summary
    print(f"\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    
    # Try to load and display key metrics
    try:
        if os.path.exists('finetuned_model_evaluation.json'):
            with open('finetuned_model_evaluation.json', 'r') as f:
                results = json.load(f)
                metrics = results.get('metrics', {})
                
                print(f"📈 FINAL RESULTS:")
                print(f"   🎯 Accuracy: {metrics.get('accuracy', 0)*100:.2f}%")
                print(f"   📊 F1 Score (Weighted): {metrics.get('f1_weighted', 0):.4f}")
                print(f"   📊 F1 Score (Macro): {metrics.get('f1_macro', 0):.4f}")
                print(f"   ⏱️  Avg Processing Time: {metrics.get('avg_processing_time', 0):.4f} seconds/video")
                print(f"   🚀 Processing Speed: {metrics.get('videos_per_second', 0):.2f} videos/second")
                print(f"   🎲 Average Confidence: {metrics.get('avg_confidence', 0):.4f}")
                print(f"   📹 Total Videos Processed: {metrics.get('total_videos', 0)}")
                
    except Exception as e:
        print(f"Could not load detailed metrics: {e}")
    
    print(f"\n📁 Generated Files:")
    generated_files = [
        'videogpt_finetuned_model.pth',
        'training_history.json',
        'finetuned_model_evaluation.json'
    ]
    
    for file in generated_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / (1024*1024)  # MB
            print(f"   ✅ {file} ({size:.1f} MB)")
        else:
            print(f"   ❌ {file} (not found)")
    
    print(f"\n🎯 Your VideoGPT+ model has been successfully fine-tuned on your 70 videos!")
    print(f"   The model is now specialized for your specific video types.")
    print(f"   You can use the fine-tuned model for future video classification tasks.")
    
    print(f"\n📖 Next Steps:")
    print(f"   1. Review the detailed results in 'finetuned_model_evaluation.json'")
    print(f"   2. Use the trained model 'videogpt_finetuned_model.pth' for inference")
    print(f"   3. Analyze training progress in 'training_history.json'")

if __name__ == "__main__":
    main()
